import com.blankj.utilcode.util.NetworkUtils;

public class NetworkUtilsTest {
    public static void testNetworkUtils() {
        // 查看NetworkUtils的可用方法
        
        // 基本网络状态检查
        boolean isConnected = NetworkUtils.isConnected();
        
        // 检查是否有可用网络
        boolean isAvailable = NetworkUtils.isAvailable();
        
        // 检查是否是WiFi连接
        boolean isWifiConnected = NetworkUtils.isWifiConnected();
        
        // 检查是否是移动数据连接
        boolean isMobileData = NetworkUtils.isMobileData();
        
        // 获取网络类型
        NetworkUtils.NetworkType networkType = NetworkUtils.getNetworkType();
        
        // 获取IP地址
        String ipAddress = NetworkUtils.getIPAddress(true);
        
        // 获取域名IP地址
        String domainAddress = NetworkUtils.getDomainAddress("www.google.com");
        
        // 检查网络是否可达 - 这个可能是我们需要的！
        // NetworkUtils.isReachable();
        
        // ping测试 - 这个也可能有用！
        // NetworkUtils.ping();
    }
}
