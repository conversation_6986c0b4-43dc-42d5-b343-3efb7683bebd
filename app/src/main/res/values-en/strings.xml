<resources>
    <string name="app_name">Supreme Smart Sewing System</string>
    <string name="checking_fingerprint_support">Checking fingerprint support…</string>
    
    <!-- APP Update Related -->
    <string name="update_title">App Update</string>
    <string name="update_available">New version found</string>
    <string name="current_version">Current version: %s</string>
    <string name="new_version">Update version: %s</string>
    <string name="update_content">Update content</string>
    <string name="update_now">Update now</string>
    <string name="update_later">Update later</string>
    <string name="downloading">Downloading…</string>
    <string name="download_progress">Download progress: %d%%</string>
    <string name="download_completed">Download completed</string>
    <string name="download_failed">Download failed</string>
    <string name="install_now">Install now</string>
    <string name="checking_update">Checking for updates…</string>
    <string name="no_update">Already the latest version</string>
    <string name="network_error">Network connection failed</string>
    <string name="file_size">File size: %s</string>
    <string name="download_speed">Download speed: %s/s</string>
    <string name="cancel_download">Cancel download</string>
    <string name="retry_download">Retry download</string>
    <string name="update_install_prompt">Download completed, click to install</string>
    <string name="update_install_failed">Installation failed</string>
    <string name="update_permission_denied">Permission denied, cannot install</string>
    <string name="check_update">Check for updates</string>
    <string name="text_command">Control command (hold to speak)</string>
    <string name="text_chat">Voice conversation</string>
    <string name="loading_update_content">Loading update content…</string>
    
    <!-- Main Activity -->
    <string name="hint_input_text">Please enter text</string>
    <string name="voice_input">Voice input</string>
    <string name="scan_qrcode">Scan QR code</string>
    <string name="scan_barcode">Scan code</string>
    <string name="ocr">OCR</string>
    <string name="face_recognition">Face recognition</string>
    <string name="fingerprint_recognition">Fingerprint recognition</string>
    <string name="standard_serial_port">Standard serial port</string>
    <string name="usb_serial_port">USB serial port</string>
    <string name="close">Close</string>
    
    <!-- Fingerprint Activity -->
    <string name="fingerprint_icon_desc">Fingerprint icon</string>
    <string name="fingerprint_title">Fingerprint Recognition</string>
    <string name="start_fingerprint_auth">Start fingerprint authentication</string>
    <string name="start_fingerprint_bind">Fingerprint binding</string>
    <string name="start_fingerprint_unbind">Fingerprint unbinding</string>
    <string name="start_fingerprint_login">Fingerprint login</string>

    <!-- Face Detection Activity -->
    <string name="flashlight_desc">Flashlight</string>
    <string name="face_register">Face\nRegistration</string>
    <string name="face_register_start">Registering face</string>
    <string name="face_register_completed">Face registration completed</string>
    <string name="face_hint">Please keep your face within the frame, better results in good lighting</string>

    <!-- Serial Activity -->
    <string name="input_data_hint">Enter data to send (hexadecimal)</string>
    <string name="send">Send</string>
    <string name="start_polling">Start polling</string>
    <string name="stop_polling">Stop polling</string>
    <string name="clear">Clear</string>
    <string name="received_data_label">Received data:</string>
    <string name="receive_data_error">Data reception error: </string>
    <string name="input_valid_hex_data">Please enter valid hexadecimal data</string>
    
    <!-- Modbus Activity -->
    <string name="default_send_data">Click send button to execute Qixing data reading</string>
    <string name="received_prefix">Received: </string>
    
    <!-- Dialog strings (from existing resources) -->
    <string name="app_dialog_title">Notice</string>
    <string name="app_dialog_cancel">Cancel</string>
    <string name="app_dialog_ok">OK</string>
    
    <!-- Activity Toast and Status Messages -->
    <string name="fingerprint_auth_title">Fingerprint Authentication</string>
    <string name="fingerprint_auth_subtitle">Please place your finger on the fingerprint sensor</string>
    <string name="fingerprint_auth_description">Use your fingerprint for authentication</string>
    <string name="fingerprint_auth_cancel">Cancel</string>
    <string name="fingerprint_auth_success">Fingerprint authentication successful!</string>
    <string name="fingerprint_auth_failed">Fingerprint authentication failed, please try again</string>
    <string name="fingerprint_auth_error">Authentication error: %s</string>
    <string name="fingerprint_auth_prompt">Please perform fingerprint authentication…</string>
    <string name="fingerprint_support_available">Device supports fingerprint recognition, authentication available</string>
    <string name="fingerprint_no_hardware">Device does not support fingerprint recognition</string>
    <string name="fingerprint_hw_unavailable">Fingerprint recognition hardware currently unavailable</string>
    <string name="fingerprint_none_enrolled">No fingerprints enrolled, please register fingerprints in system settings first</string>
    <string name="fingerprint_security_update_required">Security update required</string>
    <string name="fingerprint_unknown_error">Unknown error</string>
    
    <!-- Serial Port Messages -->
    <string name="serial_init_success">Serial port initialization successful</string>
    <string name="serial_init_failed">Serial port initialization failed</string>
    <string name="send_success">Send successful</string>
    <string name="send_failed">Send failed</string>
    <string name="start_polling_msg">Start polling</string>
    <string name="stop_polling_msg">Stop polling</string>
    <string name="usb_device_not_found">USB device not found</string>
    <string name="usb_permission_granted">USB permission granted</string>
    <string name="usb_permission_denied">USB permission denied</string>
    <string name="usb_init_success">USB serial port initialization successful</string>
    <string name="usb_init_failed">USB serial port initialization failed</string>
    <string name="serial_error">Serial port error: %1$s</string>

    <!-- Log Messages -->
    <string name="log_sync_send_response">Sync send received response: %1$s</string>
    <string name="log_sync_send_failed">Sync send failed: %1$s</string>
    <string name="log_voice_results">=========== onResults = %1$s</string>
    <string name="log_voice_matches_size">=========== matches.size() = %1$d</string>
    <string name="log_voice_error">Voice recognition error: %1$d</string>
    <string name="log_audio_error">Audio error</string>
    <string name="log_client_error">Client error</string>
    <string name="log_insufficient_permissions">Insufficient permissions</string>
    <string name="log_network_error">Network error</string>
    <string name="log_no_match">No match found</string>
    <string name="log_recognizer_busy">Recognizer busy</string>
    <string name="log_server_error">Server error</string>
    <string name="log_speech_timeout">No speech input timeout</string>
    <string name="log_voice_ready">Ready to start voice recognition</string>
    <string name="log_voice_begin">Voice start detected</string>
    <string name="log_voice_end">Voice end detected</string>
    <string name="log_voice_start_failed">Voice recognition startup failed</string>
    <string name="log_fingerprint_auth_error">Authentication error: %1$s</string>
    <string name="log_fingerprint_auth_success">Fingerprint authentication successful</string>
    <string name="log_fingerprint_auth_failed">Fingerprint authentication failed</string>
    <string name="log_camera_size_updated">Camera size updated to: %1$dx%2$d</string>
    <string name="log_upload_failed">Upload failed: %1$s</string>
    <string name="log_response_content">Response content: %1$s</string>

    <!-- Default Values -->
    <string name="default_send_data_serial" translatable="false">0203083600056794\n020300010001D5F9\n0203085200026789</string>

    <!-- Language Settings -->
    <string name="language_settings">Language Settings</string>
    <string name="select_language">Select Language</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_vietnamese">Tiếng Việt</string>
    <string name="language_indonesian">Bahasa Indonesia</string>
    <string name="back">Back</string>
    <string name="confirm">Confirm</string>

    <!-- Voice to Text Activity -->
    <string name="default_status_text">Checking permissions…</string>
    <string name="default_result_text">Transcription results will be displayed here…</string>

    <!-- Voice Input Activity -->
    <string name="voice_input_title">Voice Input (Dialog Version)</string>
    <string name="voice_recognition_result">Recognition Result</string>
    <string name="voice_input_hint">Hold the button below to start voice input…</string>
    <string name="voice_hold_to_speak">Hold to speak</string>
    <string name="voice_convert_success">Voice to text completed</string>
    <string name="voice_no_speech_detected">No speech detected</string>
    <string name="voice_input_cancelled">Voice input cancelled</string>
    <string name="voice_recognition_error">Voice recognition error: </string>

    <!-- Chat Activity -->
    <string name="voice_chat_title">Voice Chat Example</string>
    <string name="voice_input_message_hint">Enter message</string>
    <string name="voice_hold_to_speak_btn">Hold to Speak</string>
    <string name="voice_send_message_desc">Send message</string>

    <string name="toast_please_enable_manually_in_settings">Please enable manually in settings</string>
    <string name="toast_optional_permissions_denied">Optional permissions denied</string>

    <string name="dialog_power_management_settings_title">Power Management Permission Settings</string>
    <string name="dialog_power_management_settings_message_prefix">To ensure the app can receive push messages even when exited, the following permissions need to be set:\n\n</string>
    <string name="dialog_power_management_settings_message_suffix">\n\nThese settings prevent the system from killing the push service and ensure timely message reception.</string>
    <string name="dialog_power_management_settings_positive_button">Set Now</string>
    <string name="dialog_power_management_settings_negative_button">Set Later</string>

    <string name="dialog_battery_optimization_settings_title">Battery Optimization Settings</string>
    <string name="dialog_battery_optimization_settings_message">To ensure the app can receive messages normally in the background, it needs to be added to the battery optimization whitelist.\n\nAfter clicking \"Go to Settings\":\n1. Find \"Smart Sewing System\" in the popup page\n2. Select \"Don\'t optimize\" or \"Allow\"\n3. Return to the app to continue setup</string>
    <string name="dialog_battery_optimization_positive_button">Go to Settings</string>
    <string name="dialog_battery_optimization_negative_button">Skip</string>

    <string name="dialog_manual_setup_guide_title">Manual Setup Guide</string>
    <string name="dialog_manual_setup_guide_message">Please follow these steps to manually set battery optimization:\n\nMethod 1:\n1. Open phone \"Settings\"\n2. Find \"Battery\" or \"Power Management\"\n3. Find \"Battery Optimization\" or \"App Power Management\"\n4. Find \"Smart Sewing System\" and set to \"Don\'t optimize\"\n\nMethod 2:\n1. Open phone \"Settings\"\n2. Find \"App Management\" or \"App Info\"\n3. Find \"Smart Sewing System\"\n4. Find \"Battery\" option in app details\n5. Turn off \"Battery Optimization\" or set to \"Unrestricted\"</string>
    <string name="dialog_manual_setup_guide_positive_button">I understand</string>
    <string name="dialog_manual_setup_guide_negative_button">Try again</string>

    <string name="dialog_autostart_settings_title">Auto-start Permission Settings</string>
    <string name="dialog_autostart_settings_message">To ensure the push service can still run after the app exits, please enable the app\'s auto-start permission.\n\nSettings location for different phone brands:\n• Xiaomi: Security Center > App Management > Auto-start Management\n• Huawei: Phone Manager > App Launch Management\n• OPPO/OnePlus: Settings > App Management > Auto-start\n• vivo: i Manager > App Management > Auto-start\n• Others: Settings > App Management > Auto-start Management</string>
    <string name="dialog_autostart_positive_button">Go to Settings</string>
    <string name="dialog_autostart_negative_button">Skip</string>
    <string name="toast_set_autostart_hint">Please set \"%1$s\" to allow auto-start</string>
    <string name="toast_manual_autostart_hint">Please manually enable app auto-start permission in settings</string>

    <string name="toast_set_battery_or_background_hint">Please find battery or background run settings in app info</string>
    <string name="toast_power_management_setup_completed">Power management permission setup completed!</string>
    <string name="toast_power_management_partially_granted">Some permissions still need manual setup</string>

    <string name="dialog_permission_reminder_positive_button">Set Now</string>
    <string name="dialog_permission_reminder_negative_button">Remind Later</string>

    <string name="dialog_startup_management_settings_title">Startup Management Permission Settings</string>
    <string name="dialog_startup_management_settings_positive_button">Go to Settings</string>
    <string name="dialog_startup_management_settings_negative_button">Set Later</string>

    <string name="dialog_startup_management_confirm_title">Setup Completion Confirmation</string>
    <string name="dialog_startup_management_confirm_message">Have you completed the startup management settings as instructed?\n\n✓ Turn off \"Automatic Management\"\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"\n\nAfter selecting \"Yes\", the system will no longer remind you of this setting.</string>
    <string name="dialog_startup_management_confirm_positive_button">Yes, setup complete</string>
    <string name="dialog_startup_management_confirm_negative_button">Not yet, remind later</string>
    <string name="toast_startup_management_completed">Startup management permission setup completed!</string>

    <string name="toast_background_permission_setup_completed">Power management permission setup completed, app can run normally in background</string>
    <string name="toast_partial_power_permission_denied">Some power management permissions not set: %1$s</string>
    <string name="toast_background_permission_dialog_reset">Background permission dialog setting status has been reset</string>

    <string name="power_status_storage_permission">Storage Permission: </string>
    <string name="power_status_notification_permission">Notification Permission: </string>
    <string name="power_status_battery_optimization_whitelist">Battery Optimization Whitelist: </string>
    <string name="power_status_joined">Joined</string>
    <string name="power_status_not_joined">Not Joined</string>
    <string name="power_status_autostart_permission">Auto-start Permission: </string>
    <string name="power_status_granted">Granted</string>
    <string name="power_status_not_granted">Not Granted</string>
    <string name="power_status_background_run_permission">Background Run Permission: </string>
    <string name="power_status_allowed">Allowed</string>
    <string name="power_status_restricted">Restricted</string>
    <string name="power_status_entered">Entered</string>
    <string name="power_status_normal">Normal</string>
    <string name="power_status_overall_status">Overall Status: </string>
    <string name="power_status_permissions_to_set">Permissions to Set: </string>

    <!-- PermissionHelper Toast Messages -->
    <string name="toast_power_management_permission_completed">Power management permission setup completed, app can run normally in background</string>
    <string name="toast_set_power_management_permissions">Please set appropriate permissions for the app in power management</string>
    <string name="toast_find_power_management_settings">Please find power management related options in settings</string>
    <string name="toast_device_not_support_battery_optimization">Device does not support direct battery optimization setting, please set manually</string>
    <string name="toast_android_no_battery_optimization">Current Android version does not need battery optimization permission</string>
    <string name="toast_open_battery_optimization_list">Opening battery optimization settings list</string>
    <string name="toast_device_not_support_battery_settings">Device does not support battery optimization settings</string>
    <string name="toast_all_battery_optimization_failed">All battery optimization setting methods failed</string>
    <string name="toast_set_all_permissions_in_app_info">Please set all related permissions in app info</string>
    <string name="toast_manual_allow_background_run">Please manually allow app background run in settings</string>
    <string name="toast_set_battery_background_autostart_permissions">Please set \'Battery Optimization\' \'Background Run\' \'Auto-start\' to allowed/unrestricted in app info</string>

    <!-- PermissionHelper Dialog Messages -->
    <string name="dialog_background_run_settings_title">Background Run Permission Settings</string>
    <string name="dialog_background_run_settings_message">To ensure message push, please set \'Battery Optimization\' \'Background Run\' \'Auto-start\' to allowed/unrestricted on the next page.</string>
    <string name="dialog_background_run_settings_positive_button">Go to Settings</string>
    <string name="dialog_background_run_settings_negative_button">Set Later</string>

    <string name="dialog_startup_management_settings_message_xiaomi">To ensure message push, please make the following settings on the next page:\n\n✓ Turn off \"Automatic Management\"\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"</string>
    <string name="dialog_startup_management_settings_message_huawei">To ensure message push, please make the following settings on the next page:\n\n✓ Turn off \"Automatic Management\"\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"</string>
    <string name="dialog_startup_management_settings_message_oppo">To ensure message push, please make the following settings on the next page:\n\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"</string>
    <string name="dialog_startup_management_settings_message_vivo">To ensure message push, please make the following settings on the next page:\n\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Background Activity\"\n✓ Enable \"Allow High Power Consumption in Background\"</string>
    <string name="dialog_startup_management_settings_message_samsung">To ensure message push, please make the following settings on the next page:\n\n✓ Turn off \"Put app to sleep\"\n✓ Enable \"Allow Background Activity\"</string>
    <string name="dialog_startup_management_settings_message_oneplus">To ensure message push, please make the following settings on the next page:\n\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"</string>
    <string name="dialog_startup_management_settings_message_default">To ensure message push, please make the following settings on the next page:\n\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Background Activity\"\n✓ Turn off related battery optimization restrictions</string>

    <!-- Startup management permission setting dialog -->
    <string name="dialog_startup_management_base_message">To ensure the push service can still run after the app exits, please enable the app\'s auto-start permission.\n\nSettings location for different phone brands:\n</string>
    <string name="dialog_startup_management_huawei_honor_message">• Huawei/Honor: Phone Manager > App Launch Management\n</string>
    <string name="dialog_startup_management_xiaomi_redmi_message">• Xiaomi: Security Center > App Management > Auto-start Management\n</string>
    <string name="dialog_startup_management_oppo_message">• OPPO/OnePlus: Settings > App Management > Auto-start\n</string>
    <string name="dialog_startup_management_vivo_message">• vivo: i Manager > App Management > Auto-start\n</string>
    <string name="dialog_startup_management_oneplus_message">• OnePlus: Settings > App Management > Auto-start\n</string>
    <string name="dialog_startup_management_generic_message">• Others: Settings > App Management > Auto-start Management</string>

    <!-- Permission related Toast messages -->
    <string name="toast_permission_granted_success">Permission granted successfully</string>
    <string name="toast_missing_essential_permissions">Missing essential permissions: %1$s\nPlease enable manually in settings</string>
    <string name="toast_battery_optimization_success">Battery optimization setting successful!</string>
    <string name="toast_manual_battery_optimization">Please manually set battery optimization whitelist</string>
    <string name="toast_power_management_complete_background">Power management permissions setup complete, app can run normally in background</string>
    <string name="toast_manual_remaining_permissions">Please manually complete remaining permission settings for best experience</string>
    <string name="toast_background_permission_already_set">Background permissions already set</string>

    <!-- Permission display names -->
    <string name="permission_name_record_audio">Microphone</string>
    <string name="permission_name_camera">Camera</string>
    <string name="permission_name_storage">Storage</string>
    <string name="permission_name_notification">Notification</string>
    <string name="permission_name_bluetooth">Bluetooth</string>
    <string name="permission_name_location">Location</string>

    <!-- Power management permission names -->
    <string name="power_permission_battery_optimization">Battery optimization whitelist</string>
    <string name="power_permission_auto_start">Boot auto-start</string>

    <!-- Power management dialog messages -->
    <string name="dialog_power_management_intro">To ensure the app can receive push messages even when exited, the following permissions need to be set:\n\n</string>
    <string name="dialog_power_management_outro">\n\nThese settings prevent the system from killing the push service and ensure timely message reception.</string>

    <!-- Battery optimization related Toast -->
    <string name="toast_battery_optimization_guide">Please find \"Smart Sewing System\" and set to \"Don\'t optimize\"</string>
    <string name="toast_battery_optimization_app_info">Please find battery optimization settings in app info</string>

    <!-- Battery optimization manual setup dialog -->
    <string name="dialog_battery_optimization_manual_title">Manual Setup Guide</string>
    <string name="dialog_battery_optimization_manual_message">Please follow these steps to manually set battery optimization:\n\nMethod 1:\n1. Open phone \"Settings\"\n2. Find \"Battery\" or \"Power Management\"\n3. Find \"Battery Optimization\" or \"App Power Management\"\n4. Find \"Smart Sewing System\" and set to \"Don\'t optimize\"\n\nMethod 2:\n1. Open phone \"Settings\"\n2. Find \"App Management\" or \"App Info\"\n3. Find \"Smart Sewing System\"\n4. Find \"Battery\" option in app details\n5. Turn off \"Battery Optimization\" or set to \"Unrestricted\"</string>
    <string name="dialog_battery_optimization_manual_positive">I understand</string>
    <string name="dialog_battery_optimization_manual_negative">Try again</string>

    <!-- Auto-start permission dialog -->
    <string name="dialog_auto_start_title">Auto-start Permission Settings</string>
    <string name="dialog_auto_start_message">To ensure the push service can still run after the app exits, please enable the app\'s auto-start permission.\n\nSettings location for different phone brands:\n• Xiaomi: Security Center > App Management > Auto-start Management\n• Huawei: Phone Manager > App Launch Management\n• OPPO/OnePlus: Settings > App Management > Auto-start\n• vivo: i Manager > App Management > Auto-start\n• Others: Settings > App Management > Auto-start Management</string>
    <string name="dialog_auto_start_positive">Go to Settings</string>
    <string name="dialog_auto_start_negative">Skip</string>

    <!-- Auto-start related Toast -->
    <string name="toast_auto_start_guide">Please set \"Smart Sewing System\" to allow auto-start</string>
    <string name="toast_auto_start_app_info">Please find auto-start related settings in app info</string>
    <string name="toast_auto_start_manual">Please manually enable app auto-start permission in settings</string>

    <!-- Background run related Toast -->
    <string name="toast_background_run_app_info">Please find battery or background run settings in app info</string>
    <string name="toast_background_run_manual">Please manually allow app background run in settings</string>

    <!-- Power management completion related Toast -->
    <string name="toast_power_management_complete">Power management permissions setup complete!</string>
    <string name="toast_partial_permissions_manual">Some permissions still need manual setup</string>

    <!-- Power status report -->
    <string name="power_status_header">Power Management Permission Status Report</string>
    <string name="power_status_android_version">Android Version: </string>
    <string name="power_status_battery_optimization">Battery Optimization Whitelist: </string>
    <string name="power_status_auto_start">Boot Auto-start Permission: </string>
    <string name="power_status_background_run">Background Run Permission: </string>
    <string name="power_status_doze_mode">Doze Mode Status: </string>
    <string name="power_status_doze_active">Activated</string>
    <string name="power_status_doze_normal">Normal</string>
    <string name="power_status_doze_check_failed">Doze Mode Status: Check Failed</string>
    <string name="power_status_overall">Overall Status: </string>
    <string name="power_status_good">Good</string>
    <string name="power_status_needs_optimization">Needs Optimization</string>
    <string name="power_status_permissions_needed">Permissions Needed: </string>

    <!-- Power setup completion Toast -->
    <string name="toast_power_setup_complete">Power management permissions setup complete, app can run normally in background</string>
    <string name="toast_power_setup_manual_remaining">Please manually complete remaining permission settings for best experience</string>

    <!-- Power settings guidance Toast -->
    <string name="toast_power_settings_guide">Please set appropriate permissions for the app in power management</string>
    <string name="toast_power_settings_find">Please find power management related options in settings</string>
    <string name="toast_power_settings_manual">Please manually open system settings for configuration</string>

    <!-- Battery optimization debug information -->
    <string name="debug_battery_optimization_start">Starting battery optimization permission check…</string>
    <string name="debug_battery_optimization_status">Current battery optimization status: </string>
    <string name="debug_battery_optimization_ignored">Ignored</string>
    <string name="debug_battery_optimization_not_ignored">Not ignored</string>
    <string name="debug_battery_optimization_intent_resolved">Battery optimization settings Intent resolved</string>
    <string name="debug_battery_optimization_opening">Opening battery optimization settings…</string>
    <string name="debug_battery_optimization_intent_not_resolved">Battery optimization settings Intent cannot be resolved</string>
    <string name="debug_battery_optimization_not_supported">Device does not support direct battery optimization setting, please set manually</string>
    <string name="debug_battery_optimization_failed">Battery optimization permission test failed</string>
    <string name="debug_battery_optimization_test_failed">Battery optimization permission test failed: </string>
    <string name="debug_battery_optimization_android_below_6">Android versions below 6.0 do not need battery optimization permission</string>
    <string name="debug_battery_optimization_no_need">Current Android version does not need battery optimization permission</string>
    <string name="debug_battery_optimization_fallback_start">Trying fallback battery optimization setting method…</string>
    <string name="debug_battery_optimization_fallback_resolved">Fallback battery optimization settings Intent resolved</string>
    <string name="debug_battery_optimization_fallback_opening">Opening battery optimization settings list</string>
    <string name="debug_battery_optimization_fallback_not_resolved">Fallback battery optimization settings Intent cannot be resolved</string>
    <string name="debug_battery_optimization_fallback_not_supported">Device does not support battery optimization settings</string>
    <string name="debug_battery_optimization_fallback_failed">Fallback battery optimization setting failed</string>
    <string name="debug_battery_optimization_all_failed">All battery optimization setting methods failed</string>

    <!-- Simplified background permission setting Toast -->
    <string name="toast_simple_background_app_info">Please set all related permissions in app info</string>
    <string name="toast_simple_background_manual">Please manually allow app background run in settings</string>

    <!-- Background permission direct setting Toast -->
    <string name="toast_background_permission_direct_guide">Please set \'Battery Optimization\' \'Background Run\' \'Auto-start\' to allowed/unrestricted in app info</string>
    <string name="toast_background_permission_direct_manual">Please manually allow app background run in settings</string>

    <!-- Background permission setup completion confirmation dialog -->
    <string name="dialog_background_permission_completion_title">Setup Completion Confirmation</string>
    <string name="dialog_background_permission_completion_message">Have you completed the startup management settings as instructed?\n\n✓ Turn off \"Automatic Management\"\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"\n\nAfter selecting \"Yes\", the system will no longer remind you of this setting.</string>
    <string name="dialog_background_permission_completion_positive">Yes, setup complete</string>
    <string name="dialog_background_permission_completion_negative">Not yet, remind later</string>
    <string name="toast_background_permission_completion">Startup management permissions setup complete!</string>

    <!-- Power management permission dialog -->
    <string name="dialog_power_management_title">Power Management Permission Settings</string>
    <string name="dialog_power_management_message">To ensure the app can receive push messages even when exited, the following permissions need to be set:\n\n%1$s\n\nThese settings prevent the system from killing the push service and ensure timely message reception.</string>

    <!-- Battery optimization dialog -->
    <string name="dialog_battery_optimization_title">Battery Optimization Settings</string>
    <string name="dialog_battery_optimization_message">To ensure the app can receive messages normally in the background, it needs to be added to the battery optimization whitelist.\n\nAfter clicking \"Go to Settings\":\n1. Find \"Smart Sewing System\" in the popup page\n2. Select \"Don\'t optimize\" or \"Allow\"\n3. Return to the app to continue setup</string>
    <string name="dialog_battery_optimization_positive">Go to Settings</string>
    <string name="dialog_battery_optimization_negative">Skip</string>

    <!-- Battery optimization manual guide -->
    <string name="dialog_battery_manual_title">Manual Setup Guide</string>
    <string name="dialog_battery_manual_message">Please follow these steps to manually set battery optimization:\n\nMethod 1:\n1. Open phone \"Settings\"\n2. Find \"Battery\" or \"Power Management\"\n3. Find \"Battery Optimization\" or \"App Power Management\"\n4. Find \"Smart Sewing System\" and set to \"Don\'t optimize\"\n\nMethod 2:\n1. Open phone \"Settings\"\n2. Find \"App Management\" or \"App Info\"\n3. Find \"Smart Sewing System\"\n4. Find \"Battery\" option in app details\n5. Turn off \"Battery Optimization\" or set to \"Unrestricted\"</string>
    <string name="dialog_battery_manual_positive">I understand</string>
    <string name="dialog_battery_manual_negative">Try again</string>

    <!-- Auto-start permission dialog -->
    <string name="dialog_autostart_title">Auto-start Permission Settings</string>
    <string name="dialog_autostart_message">To ensure the push service can still run after the app exits, please enable the app\'s auto-start permission.\n\nSettings location for different phone brands:\n• Xiaomi: Security Center > App Management > Auto-start Management\n• Huawei: Phone Manager > App Launch Management\n• OPPO/OnePlus: Settings > App Management > Auto-start\n• vivo: i Manager > App Management > Auto-start\n• Others: Settings > App Management > Auto-start Management</string>
    <string name="dialog_autostart_positive">Go to Settings</string>
    <string name="dialog_autostart_negative">Skip</string>

    <!-- Background run permission dialog -->
    <string name="dialog_background_run_title">Background Run Permission Settings</string>
    <string name="dialog_background_run_message">To ensure the push service can run continuously in the background, please allow the app to run in the background.\n\nSetup method:\n• Find \"Battery\" or \"Background Run\" option in app info page\n• Set to \"Unrestricted\" or \"Allow background run\"\n• Turn off \"Adaptive Battery\" restrictions for this app</string>
    <string name="dialog_background_run_positive">Go to Settings</string>
    <string name="dialog_background_run_negative">Complete</string>

    <!-- Permission reminder dialog -->
    <string name="dialog_permission_reminder_title">Permission Reminder</string>
    <string name="dialog_permission_reminder_message">The following permissions are not set and may affect message reception:\n\n%1$s\n\nSet them now?</string>
    <string name="dialog_permission_reminder_positive">Set Now</string>
    <string name="dialog_permission_reminder_negative">Remind Later</string>

    <!-- Permission reminder dialog message components -->
    <string name="dialog_permission_reminder_message_prefix">The following permissions are not set and may affect message reception:\n\n</string>
    <string name="dialog_permission_reminder_message_suffix">\n\nSet them now?</string>

    <!-- Simplified background permission dialog -->
    <string name="dialog_simple_background_title">Background Run Permission Settings</string>
    <string name="dialog_simple_background_message">To ensure message push, please set \'Battery Optimization\' \'Background Run\' \'Auto-start\' to allowed/unrestricted on the next page.</string>
    <string name="dialog_simple_background_positive">Go to Settings</string>

    <!-- Startup management permission settings dialog -->
    <string name="dialog_startup_management_title">Startup Management Permission Settings</string>
    <string name="dialog_startup_management_positive">Go to Settings</string>
    <string name="dialog_startup_management_negative">Set Later</string>

    <!-- Startup management permission setup completion Toast -->
    <string name="toast_startup_management_complete">Startup management permissions setup complete!</string>
    <string name="toast_startup_management_reset">Startup management permission setting status has been reset</string>

    <!-- Setup completion confirmation dialog -->
    <string name="dialog_setup_confirmation_title">Setup Completion Confirmation</string>
    <string name="dialog_setup_confirmation_message">Have you completed the startup management settings as instructed?\n\n✓ Turn off automatic management\n✓ Allow auto-start\n✓ Allow associated startup\n✓ Allow background activity\n\nAfter selecting \"Yes\", the system will no longer remind you of this setting.</string>
    <string name="dialog_setup_confirmation_positive">Yes, setup complete</string>
    <string name="dialog_setup_confirmation_negative">Not yet, remind later</string>

    <!-- Background permission setup completion confirmation dialog -->
    <string name="dialog_background_setup_confirmation_title">Setup Completion Confirmation</string>
    <string name="dialog_background_setup_confirmation_message">Have you completed the startup management settings as instructed?\n\n✓ Turn off \"Automatic Management\"\n✓ Enable \"Allow Auto-start\"\n✓ Enable \"Allow Associated Startup\"\n✓ Enable \"Allow Background Activity\"\n\nAfter selecting \"Yes\", the system will no longer remind you of this setting.</string>
    <string name="dialog_background_setup_confirmation_positive">Yes, setup complete</string>
    <string name="dialog_background_setup_confirmation_negative">Not yet, remind later</string>

    <!-- Toast hint messages -->
    <string name="toast_find_smart_sewing_system">Please find \"Smart Sewing System\" and set to \"Don\'t optimize\"</string>
    <string name="toast_find_battery_optimization">Please find battery optimization settings in app info</string>
    <string name="toast_allow_autostart">Please set \"Smart Sewing System\" to allow auto-start</string>
    <string name="toast_find_autostart_settings">Please find auto-start related settings in app info</string>
    <string name="toast_manual_autostart">Please manually enable app auto-start permission in settings</string>
    <string name="toast_find_battery_background">Please find battery or background run settings in app info</string>
    <string name="toast_manual_background_run">Please manually allow app background run in settings</string>
    <string name="toast_set_power_management">Please set appropriate permissions for the app in power management</string>
    <string name="toast_find_power_management">Please find power management related options in settings</string>
    <string name="toast_manual_system_settings">Please manually open system settings for configuration</string>
    <string name="toast_opening_battery_optimization">Opening battery optimization settings…</string>
    <string name="toast_device_not_support_direct">Device does not support direct battery optimization setting, please set manually</string>
    <string name="toast_battery_optimization_test_failed">Battery optimization permission test failed: %1$s</string>
    <string name="toast_no_battery_optimization_needed">Current Android version does not need battery optimization permission</string>
    <string name="toast_opening_battery_list">Opening battery optimization settings list</string>
    <string name="toast_device_not_support_battery">Device does not support battery optimization settings</string>
    <string name="toast_all_battery_methods_failed">All battery optimization setting methods failed</string>
    <string name="toast_set_all_power_permissions_hint">Please set all related permissions in app info</string>
    <string name="toast_manual_background_run_hint">Please manually allow app background run in settings</string>
    <string name="toast_set_all_power_permissions_hint_detailed">Please set \'Battery Optimization\', \'Background Run\', and \'Auto-start\' to allow/unrestricted in app info</string>
    <string name="toast_background_dialog_reset">Background permission dialog setting status has been reset</string>

    <!-- Power management permission dialog buttons -->
    <string name="dialog_power_management_positive">Set Now</string>
    <string name="dialog_power_management_negative">Set Later</string>

    <!-- Power management permission status -->
    <string name="power_permission_battery_whitelist">Battery optimization whitelist</string>
    <string name="power_permission_autostart">Boot auto-start</string>
    <string name="power_permission_background_run">Background run</string>

    <!-- Voice to Text Activity -->
    <string name="voice_to_text_title">Voice to Text</string>
    <string name="voice_to_text_test_whisper">Test Whisper</string>
    <string name="voice_to_text_start_record">Start Recording</string>
    <string name="voice_to_text_stop_record">Stop Recording</string>
    <string name="voice_to_text_start_convert">Start Convert</string>
    <string name="voice_to_text_force_cn">Force Chinese</string>
    <string name="voice_to_text_test_api">Test API</string>
    <string name="voice_to_text_json_convert">JSON Convert</string>

    <!-- Voice Input Dialog -->
    <string name="dialog_voice_input_speak">Please speak</string>
    <string name="dialog_voice_input_listening">Listening...</string>
    <string name="dialog_voice_input_cancel">Cancel</string>
    <string name="dialog_voice_input_send">Send</string>
    <string name="dialog_voice_input_swipe_hint">Swipe up to send, swipe down to cancel</string>

    <!-- Chat Items -->
    <string name="chat_text_sample">Sample text</string>
    <string name="voice_item_time">Time</string>
    <string name="voice_item_seconds">seconds</string>
    <string name="voice_item_converting">Converting...</string>

    <!-- Test Battery Optimization -->
    <string name="test_battery_optimization">Test Battery Optimization</string>

    <!-- Voice Recognition Toast Messages -->
    <string name="toast_no_recognition">No speech recognized</string>
    <string name="toast_voice_recognition_error">Voice recognition error: %1$s</string>
    <string name="toast_save_voice_failed">Failed to save voice</string>
    <string name="toast_voice_data_unavailable">Voice data unavailable</string>
    <string name="toast_voice_file_not_found">Voice file not found</string>
    <string name="toast_playback_failed">Playback failed</string>
    <string name="toast_playback_failed_with_message">Playback failed: %1$s</string>
    <string name="toast_invalid_voice_data">Invalid voice data</string>
    <string name="toast_voice_to_text_converting">Converting voice to text...</string>
    <string name="toast_no_text_content">No text content</string>
    <string name="toast_voice_to_text_failed">Voice to text conversion failed: %1$s</string>

</resources>
