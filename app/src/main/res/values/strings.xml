<resources>
    <string name="app_name">舒普智能缝纫系统</string>
    <string name="checking_fingerprint_support">正在检查指纹识别支持…</string>
    
    <!-- APP更新相关 -->
    <string name="update_title">应用更新</string>
    <string name="update_available">发现新版本</string>
    <string name="current_version">当前版本：%s</string>
    <string name="new_version">更新版本：%s</string>
    <string name="update_content">更新内容</string>
    <string name="update_now">立即更新</string>
    <string name="update_later">稍后更新</string>
    <string name="downloading">正在下载…</string>
    <string name="download_progress">下载进度：%d%%</string>
    <string name="download_completed">下载完成</string>
    <string name="download_failed">下载失败</string>
    <string name="install_now">立即安装</string>
    <string name="checking_update">正在检查更新…</string>
    <string name="no_update">已是最新版本</string>
    <string name="network_error">网络连接失败</string>
    <string name="file_size">文件大小：%s</string>
    <string name="download_speed">下载速度：%s/s</string>
    <string name="cancel_download">取消下载</string>
    <string name="retry_download">重试下载</string>
    <string name="update_install_prompt">下载完成，点击安装</string>
    <string name="update_install_failed">安装失败</string>
    <string name="update_permission_denied">权限被拒绝，无法安装</string>
    <string name="check_update">检查更新</string>
    <string name="text_command">控制命令(按住说话)</string>
    <string name="text_chat">语音对话</string>
    <string name="loading_update_content">正在加载更新内容…</string>
    
    <!-- Main Activity -->
    <string name="hint_input_text">请输入文字</string>
    <string name="voice_input">语音输入</string>
    <string name="scan_qrcode">扫二维码</string>
    <string name="scan_barcode">扫码</string>
    <string name="ocr">OCR</string>
    <string name="face_recognition">人脸识别</string>
    <string name="fingerprint_recognition">指纹识别</string>
    <string name="standard_serial_port">标准串口</string>
    <string name="usb_serial_port">USB串口</string>
    <string name="close">关闭</string>
    
    <!-- Fingerprint Activity -->
    <string name="fingerprint_icon_desc">指纹图标</string>
    <string name="fingerprint_title">指纹识别</string>
    <string name="start_fingerprint_auth">开始指纹认证</string>
    <string name="start_fingerprint_bind">指纹绑定</string>
    <string name="start_fingerprint_unbind">指纹解绑</string>
    <string name="start_fingerprint_login">指纹登录</string>

    <!-- Face Detection Activity -->
    <string name="flashlight_desc">闪光灯</string>
    <string name="face_register">人脸\n注册</string>
    <string name="face_register_start">正在注册人脸</string>
    <string name="face_register_completed">人脸注册完成</string>
    <string name="face_hint">请保持人脸在框内，光线充足时效果更佳</string>

    <!-- Serial Activity -->
    <string name="input_data_hint">输入要发送的数据（十六进制）</string>
    <string name="send">发送</string>
    <string name="start_polling">开始轮询</string>
    <string name="stop_polling">停止轮询</string>
    <string name="clear">清除</string>
    <string name="received_data_label">接收到的数据：</string>
    <string name="receive_data_error">数据接收错误: </string>
    <string name="input_valid_hex_data">请输入有效的十六进制数据</string>
    
    <!-- Modbus Activity -->
    <string name="default_send_data">点击发送按钮执行琦星数据读取</string>
    <string name="received_prefix">接收: </string>
    
    <!-- Dialog strings (from existing resources) -->
    <string name="app_dialog_title">提示</string>
    <string name="app_dialog_cancel">取消</string>
    <string name="app_dialog_ok">确定</string>
    
    <!-- Activity Toast and Status Messages -->
    <string name="fingerprint_auth_title">指纹认证</string>
    <string name="fingerprint_auth_subtitle">请将手指放在指纹传感器上</string>
    <string name="fingerprint_auth_description">使用您的指纹进行身份验证</string>
    <string name="fingerprint_auth_cancel">取消</string>
    <string name="fingerprint_auth_success">指纹认证成功！</string>
    <string name="fingerprint_auth_failed">指纹认证失败，请重试</string>
    <string name="fingerprint_auth_error">认证错误: %s</string>
    <string name="fingerprint_auth_prompt">请进行指纹认证…</string>
    <string name="fingerprint_support_available">设备支持指纹识别，可以进行认证</string>
    <string name="fingerprint_no_hardware">设备不支持指纹识别</string>
    <string name="fingerprint_hw_unavailable">指纹识别硬件当前不可用</string>
    <string name="fingerprint_none_enrolled">未注册指纹，请先在系统设置中注册指纹</string>
    <string name="fingerprint_security_update_required">需要安全更新</string>
    <string name="fingerprint_unknown_error">未知错误</string>
    
    <!-- Serial Port Messages -->
    <string name="serial_init_success">串口初始化成功</string>
    <string name="serial_init_failed">串口初始化失败</string>
    <string name="send_success">发送成功</string>
    <string name="send_failed">发送失败</string>
    <string name="start_polling_msg">开始轮询</string>
    <string name="stop_polling_msg">停止轮询</string>
    <string name="usb_device_not_found">未找到USB设备</string>
    <string name="usb_permission_granted">USB权限已授予</string>
    <string name="usb_permission_denied">USB权限被拒绝</string>
    <string name="usb_init_success">USB串口初始化成功</string>
    <string name="usb_init_failed">USB串口初始化失败</string>
    <string name="serial_error">串口错误: %1$s</string>

    <!-- Log Messages -->
    <string name="log_sync_send_response">同步发送接收到响应: %1$s</string>
    <string name="log_sync_send_failed">同步发送失败: %1$s</string>
    <string name="log_voice_results">=========== onResults = %1$s</string>
    <string name="log_voice_matches_size">=========== matches.size() = %1$d</string>
    <string name="log_voice_error">语音识别错误: %1$d</string>
    <string name="log_audio_error">音频错误</string>
    <string name="log_client_error">客户端错误</string>
    <string name="log_insufficient_permissions">权限不足</string>
    <string name="log_network_error">网络错误</string>
    <string name="log_no_match">无匹配结果</string>
    <string name="log_recognizer_busy">识别器忙</string>
    <string name="log_server_error">服务器错误</string>
    <string name="log_speech_timeout">无语音输入超时</string>
    <string name="log_voice_ready">准备开始语音识别</string>
    <string name="log_voice_begin">检测到语音开始</string>
    <string name="log_voice_end">检测到语音结束</string>
    <string name="log_voice_start_failed">语音识别启动失败</string>
    <string name="log_fingerprint_auth_error">认证错误: %1$s</string>
    <string name="log_fingerprint_auth_success">指纹认证成功</string>
    <string name="log_fingerprint_auth_failed">指纹认证失败</string>
    <string name="log_camera_size_updated">相机尺寸更新为: %1$dx%2$d</string>
    <string name="log_upload_failed">上传失败: %1$s</string>
    <string name="log_response_content">响应内容: %1$s</string>

    <!-- Default Values -->
    <string name="default_send_data_serial" translatable="false">0203083600056794\n020300010001D5F9\n0203085200026789</string>

    <!-- Language Settings -->
    <string name="language_settings">语言设置</string>
    <string name="select_language">选择语言</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_vietnamese">Tiếng Việt</string>
    <string name="language_indonesian">Bahasa Indonesia</string>
    <string name="back">返回</string>
    <string name="confirm">确认</string>

    <!-- Voice to Text Activity -->
    <string name="default_status_text">正在检查权限…</string>
    <string name="default_result_text">转录结果将显示在这里…</string>

    <!-- Voice Input Activity -->
    <string name="voice_input_title">语音输入 (对话框版)</string>
    <string name="voice_recognition_result">识别结果</string>
    <string name="voice_input_hint">按住下方按钮开始语音输入…</string>
    <string name="voice_hold_to_speak">按住说话</string>
    <string name="voice_convert_success">语音转文字完成</string>
    <string name="voice_no_speech_detected">未识别到语音</string>
    <string name="voice_input_cancelled">语音输入已取消</string>
    <string name="voice_recognition_error">语音识别错误: </string>

    <!-- Chat Activity -->
    <string name="voice_chat_title">语音聊天示例</string>
    <string name="voice_input_message_hint">输入消息</string>
    <string name="voice_hold_to_speak_btn">按住 说话</string>
    <string name="voice_send_message_desc">发送消息</string>

    <string name="toast_please_enable_manually_in_settings">请在设置中手动开启</string>
    <string name="toast_optional_permissions_denied">可选权限被拒绝</string>

    <string name="dialog_power_management_settings_title">电源管理权限设置</string>
    <string name="dialog_power_management_settings_message_prefix">为确保应用在退出时也能收到推送消息，需要设置以下权限：\n\n</string>
    <string name="dialog_power_management_settings_message_suffix">\n这些设置可以防止系统杀死推送服务，确保消息及时接收。</string>
    <string name="dialog_power_management_settings_positive_button">立即设置</string>
    <string name="dialog_power_management_settings_negative_button">稍后设置</string>

    <string name="dialog_battery_optimization_settings_title">电池优化设置</string>
    <string name="dialog_battery_optimization_settings_message">为确保应用能在后台正常接收消息，需要将应用加入电池优化白名单。\n\n点击"去设置"后：\n1. 在弹出的页面中找到"智能缝纫系统"\n2. 选择"不优化"或"允许"\n3. 返回应用继续设置</string>
    <string name="dialog_battery_optimization_positive_button">去设置</string>
    <string name="dialog_battery_optimization_negative_button">跳过</string>

    <string name="dialog_manual_setup_guide_title">手动设置指导</string>
    <string name="dialog_manual_setup_guide_message">请按以下步骤手动设置电池优化：\n\n方法一：\n1. 打开手机"设置"\n2. 找到"电池"或"电源管理"\n3. 找到"电池优化"或"应用耗电管理"\n4. 找到"智能缝纫系统"并设置为"不优化"\n\n方法二：\n1. 打开手机"设置"\n2. 找到"应用管理"或"应用信息"\n3. 找到"智能缝纫系统"\n4. 在应用详情中找到"电池"选项\n5. 关闭"电池优化"或设置为"无限制"</string>
    <string name="dialog_manual_setup_guide_positive_button">我知道了</string>
    <string name="dialog_manual_setup_guide_negative_button">重新尝试</string>

    <string name="dialog_autostart_settings_title">自启动权限设置</string>
    <string name="dialog_autostart_settings_message">为确保推送服务在应用退出后仍能运行，请开启应用的自启动权限。\n\n不同手机品牌的设置位置：\n• 小米：安全中心 > 应用管理 > 自启动管理\n• 华为：手机管家 > 应用启动管理\n• OPPO/一加：设置 > 应用管理 > 自启动\n• vivo：i管家 > 应用管理 > 自启动\n• 其他：设置 > 应用管理 > 自启动管理</string>
    <string name="dialog_autostart_positive_button">去设置</string>
    <string name="dialog_autostart_negative_button">跳过</string>
    <string name="toast_set_autostart_hint">请将"%1$s"设置为允许自启动</string>
    <string name="toast_manual_autostart_hint">请手动在设置中开启应用自启动权限</string>
    <string name="toast_set_battery_or_background_hint">请在应用信息中找到电池或后台运行设置</string>
    <string name="toast_power_management_setup_completed">电源管理权限设置完成！</string>
    <string name="toast_power_management_partially_granted">部分权限仍需手动设置</string>

    <string name="dialog_permission_reminder_positive_button">立即设置</string>
    <string name="dialog_permission_reminder_negative_button">稍后提醒</string>

    <string name="dialog_startup_management_settings_title">启动管理权限设置</string>
    <string name="dialog_startup_management_settings_positive_button">去设置</string>
    <string name="dialog_startup_management_settings_negative_button">稍后设置</string>

    <string name="dialog_startup_management_confirm_title">设置完成确认</string>
    <string name="dialog_startup_management_confirm_message">您是否已经按照说明完成了启动管理设置？\n\n✓ 关闭"自动管理"\n✓ 开启"允许自启动"\n✓ 开启"允许关联启动"\n✓ 开启"允许后台活动"\n\n选择"是"后，系统将不再提醒此设置。</string>
    <string name="dialog_startup_management_confirm_positive_button">是，已设置完成</string>
    <string name="dialog_startup_management_confirm_negative_button">还没有，稍后再说</string>
    <string name="toast_startup_management_completed">启动管理权限设置完成！</string>

    <string name="toast_background_permission_setup_completed">电源管理权限设置完成，应用可以在后台正常运行</string>
    <string name="toast_partial_power_permission_denied">部分电源管理权限未设置: %1$s</string>
    <string name="toast_background_permission_dialog_reset">后台权限对话框设置状态已重置</string>

    <string name="power_status_storage_permission">存储权限: </string>
    <string name="power_status_notification_permission">通知权限: </string>
    <string name="power_status_battery_optimization_whitelist">电池优化白名单: </string>
    <string name="power_status_joined">已加入</string>
    <string name="power_status_not_joined">未加入</string>
    <string name="power_status_autostart_permission">开机自启动权限: </string>
    <string name="power_status_granted">已授予</string>
    <string name="power_status_not_granted">未授予</string>
    <string name="power_status_background_run_permission">后台运行权限: </string>
    <string name="power_status_allowed">已允许</string>
    <string name="power_status_restricted">受限制</string>
    <string name="power_status_entered">已进入</string>
    <string name="power_status_normal">正常</string>
    <string name="power_status_overall_status">总体状态: </string>
    <string name="power_status_permissions_to_set">需要设置的权限: </string>

    <!-- PermissionHelper Toast Messages -->
    <string name="toast_power_management_permission_completed">电源管理权限设置完成，应用可以在后台正常运行</string>
    <string name="toast_set_power_management_permissions">请在电源管理中为应用设置合适的权限</string>
    <string name="toast_find_power_management_settings">请在设置中找到电源管理相关选项</string>
    <string name="toast_device_not_support_battery_optimization">设备不支持直接设置电池优化，请手动设置</string>
    <string name="toast_android_no_battery_optimization">当前Android版本无需电池优化权限</string>
    <string name="toast_open_battery_optimization_list">打开电池优化设置列表</string>
    <string name="toast_device_not_support_battery_settings">设备不支持电池优化设置</string>
    <string name="toast_all_battery_optimization_failed">所有电池优化设置方案都失败</string>
    <string name="toast_set_all_permissions_in_app_info">请在应用信息中设置所有相关权限</string>
    <string name="toast_manual_allow_background_run">请手动在设置中允许应用后台运行</string>
    <string name="toast_set_battery_background_autostart_permissions">请在应用信息中设置\'电池优化\'\'后台运行\'\'自启动\'为允许/不限制</string>

    <!-- PermissionHelper Dialog Messages -->
    <string name="dialog_background_run_settings_title">后台运行权限设置</string>
    <string name="dialog_background_run_settings_message">为确保消息推送，请在下个页面将\'电池优化\'\'后台运行\'\'自启动\'都设置为允许/不限制。</string>
    <string name="dialog_background_run_settings_positive_button">去设置</string>
    <string name="dialog_background_run_settings_negative_button">稍后设置</string>

    <string name="dialog_startup_management_settings_message_xiaomi">为确保消息推送，请在下个页面进行以下设置：

✓ 关闭"自动管理"
✓ 开启"允许自启动"
✓ 开启"允许关联启动"
✓ 开启"允许后台活动"</string>
    <string name="dialog_startup_management_settings_message_huawei">为确保消息推送，请在下个页面进行以下设置：

✓ 关闭"自动管理"
✓ 开启"允许自启动"
✓ 开启"允许关联启动"
✓ 开启"允许后台活动"</string>
    <string name="dialog_startup_management_settings_message_oppo">为确保消息推送，请在下个页面进行以下设置：

✓ 开启"允许自启动"
✓ 开启"允许关联启动"
✓ 开启"允许后台活动"</string>
    <string name="dialog_startup_management_settings_message_vivo">为确保消息推送，请在下个页面进行以下设置：

✓ 开启"允许自启动"
✓ 开启"允许后台活动"
✓ 开启"允许后台高耗电"</string>
    <string name="dialog_startup_management_settings_message_samsung">为确保消息推送，请在下个页面进行以下设置：

✓ 关闭"使应用进入睡眠状态"
✓ 开启"允许后台活动"</string>
    <string name="dialog_startup_management_settings_message_oneplus">为确保消息推送，请在下个页面进行以下设置：

✓ 开启"允许自启动"
✓ 开启"允许关联启动"
✓ 开启"允许后台活动"</string>
    <string name="dialog_startup_management_settings_message_default">为确保消息推送，请在下个页面进行以下设置：

✓ 开启"允许自启动"
✓ 开启"允许后台活动"
✓ 关闭相关的电池优化限制</string>

    <!-- 启动管理权限设置对话框 -->
    <string name="dialog_startup_management_base_message">为确保推送服务在应用退出后仍能运行，请开启应用的自启动权限。\n\n不同手机品牌的设置位置：\n</string>
    <string name="dialog_startup_management_huawei_honor_message">• 华为/荣耀：手机管家 > 应用启动管理\n</string>
    <string name="dialog_startup_management_xiaomi_redmi_message">• 小米：安全中心 > 应用管理 > 自启动管理\n</string>
    <string name="dialog_startup_management_oppo_message">• OPPO/一加：设置 > 应用管理 > 自启动\n</string>
    <string name="dialog_startup_management_vivo_message">• vivo：i管家 > 应用管理 > 自启动\n</string>
    <string name="dialog_startup_management_oneplus_message">• 一加：设置 > 应用管理 > 自启动\n</string>
    <string name="dialog_startup_management_generic_message">• 其他：设置 > 应用管理 > 自启动管理</string>

    <!-- 权限相关Toast消息 -->
    <string name="toast_permission_granted_success">权限授予成功</string>
    <string name="toast_missing_essential_permissions">缺少必需权限：%1$s\n请在设置中手动开启</string>
    <string name="toast_battery_optimization_success">电池优化设置成功！</string>
    <string name="toast_manual_battery_optimization">请手动设置电池优化白名单</string>
    <string name="toast_power_management_complete_background">电源管理权限设置完成，应用可以在后台正常运行</string>
    <string name="toast_manual_remaining_permissions">请手动完成剩余权限设置以确保最佳体验</string>
    <string name="toast_background_permission_already_set">后台权限已设置</string>

    <!-- 权限显示名称 -->
    <string name="permission_name_record_audio">录音</string>
    <string name="permission_name_camera">摄像头</string>
    <string name="permission_name_storage">存储</string>
    <string name="permission_name_notification">通知</string>
    <string name="permission_name_bluetooth">蓝牙</string>
    <string name="permission_name_location">位置</string>

    <!-- 电源管理权限名称 -->
    <string name="power_permission_battery_optimization">电池优化白名单</string>
    <string name="power_permission_auto_start">开机自启动</string>

    <!-- 电源管理对话框消息 -->
    <string name="dialog_power_management_intro">为确保应用在退出时也能收到推送消息，需要设置以下权限：\n\n</string>
    <string name="dialog_power_management_outro">\n\n这些设置可以防止系统杀死推送服务，确保消息及时接收。</string>

    <!-- 电池优化相关Toast -->
    <string name="toast_battery_optimization_guide">请找到"智能缝纫系统"并设置为"不优化"</string>
    <string name="toast_battery_optimization_app_info">请在应用信息中找到电池优化设置</string>

    <!-- 电池优化手动设置对话框 -->
    <string name="dialog_battery_optimization_manual_title">手动设置指导</string>
    <string name="dialog_battery_optimization_manual_message">请按以下步骤手动设置电池优化：\n\n方法一：\n1. 打开手机"设置"\n2. 找到"电池"或"电源管理"\n3. 找到"电池优化"或"应用耗电管理"\n4. 找到"智能缝纫系统"并设置为"不优化"\n\n方法二：\n1. 打开手机"设置"\n2. 找到"应用管理"或"应用信息"\n3. 找到"智能缝纫系统"\n4. 在应用详情中找到"电池"选项\n5. 关闭"电池优化"或设置为"无限制"</string>
    <string name="dialog_battery_optimization_manual_positive">我知道了</string>
    <string name="dialog_battery_optimization_manual_negative">重新尝试</string>

    <!-- 自启动权限对话框 -->
    <string name="dialog_auto_start_title">自启动权限设置</string>
    <string name="dialog_auto_start_message">为确保推送服务在应用退出后仍能运行，请开启应用的自启动权限。\n\n不同手机品牌的设置位置：\n• 小米：安全中心 > 应用管理 > 自启动管理\n• 华为：手机管家 > 应用启动管理\n• OPPO/一加：设置 > 应用管理 > 自启动\n• vivo：i管家 > 应用管理 > 自启动\n• 其他：设置 > 应用管理 > 自启动管理</string>
    <string name="dialog_auto_start_positive">去设置</string>
    <string name="dialog_auto_start_negative">跳过</string>

    <!-- 自启动相关Toast -->
    <string name="toast_auto_start_guide">请将"智能缝纫系统"设置为允许自启动</string>
    <string name="toast_auto_start_app_info">请在应用信息中找到自启动相关设置</string>
    <string name="toast_auto_start_manual">请手动在设置中开启应用自启动权限</string>

    <!-- 后台运行相关Toast -->
    <string name="toast_background_run_app_info">请在应用信息中找到电池或后台运行设置</string>
    <string name="toast_background_run_manual">请手动在设置中允许应用后台运行</string>

    <!-- 电源管理完成相关Toast -->
    <string name="toast_power_management_complete">电源管理权限设置完成！</string>
    <string name="toast_partial_permissions_manual">部分权限仍需手动设置</string>

    <!-- 电源状态报告 -->
    <string name="power_status_header">电源管理权限状态报告</string>
    <string name="power_status_android_version">Android版本: </string>
    <string name="power_status_battery_optimization">电池优化白名单: </string>
    <string name="power_status_auto_start">开机自启动权限: </string>
    <string name="power_status_background_run">后台运行权限: </string>
    <string name="power_status_doze_mode">Doze模式状态: </string>
    <string name="power_status_doze_active">已激活</string>
    <string name="power_status_doze_normal">正常</string>
    <string name="power_status_doze_check_failed">Doze模式状态: 检查失败</string>
    <string name="power_status_overall">总体状态: </string>
    <string name="power_status_good">良好</string>
    <string name="power_status_needs_optimization">需要优化</string>
    <string name="power_status_permissions_needed">需要设置的权限: </string>

    <!-- 电源设置完成Toast -->
    <string name="toast_power_setup_complete">电源管理权限设置完成，应用可以在后台正常运行</string>
    <string name="toast_power_setup_manual_remaining">请手动完成剩余权限设置以确保最佳体验</string>

    <!-- 电源设置指导Toast -->
    <string name="toast_power_settings_guide">请在电源管理中为应用设置合适的权限</string>
    <string name="toast_power_settings_find">请在设置中找到电源管理相关选项</string>
    <string name="toast_power_settings_manual">请手动打开系统设置进行配置</string>

    <!-- 电池优化调试信息 -->
    <string name="debug_battery_optimization_start">开始检查电池优化权限…</string>
    <string name="debug_battery_optimization_status">当前电池优化状态: </string>
    <string name="debug_battery_optimization_ignored">已忽略</string>
    <string name="debug_battery_optimization_not_ignored">未忽略</string>
    <string name="debug_battery_optimization_intent_resolved">电池优化设置Intent已解析</string>
    <string name="debug_battery_optimization_opening">正在打开电池优化设置…</string>
    <string name="debug_battery_optimization_intent_not_resolved">电池优化设置Intent无法解析</string>
    <string name="debug_battery_optimization_not_supported">设备不支持直接设置电池优化，请手动设置</string>
    <string name="debug_battery_optimization_failed">电池优化权限测试失败</string>
    <string name="debug_battery_optimization_test_failed">电池优化权限测试失败: </string>
    <string name="debug_battery_optimization_android_below_6">Android 6.0以下版本无需电池优化权限</string>
    <string name="debug_battery_optimization_no_need">当前Android版本无需电池优化权限</string>
    <string name="debug_battery_optimization_fallback_start">尝试备用电池优化设置方案…</string>
    <string name="debug_battery_optimization_fallback_resolved">备用电池优化设置Intent已解析</string>
    <string name="debug_battery_optimization_fallback_opening">打开电池优化设置列表</string>
    <string name="debug_battery_optimization_fallback_not_resolved">备用电池优化设置Intent无法解析</string>
    <string name="debug_battery_optimization_fallback_not_supported">设备不支持电池优化设置</string>
    <string name="debug_battery_optimization_fallback_failed">备用电池优化设置失败</string>
    <string name="debug_battery_optimization_all_failed">所有电池优化设置方案都失败</string>

    <!-- 简化后台权限设置Toast -->
    <string name="toast_simple_background_app_info">请在应用信息中设置所有相关权限</string>
    <string name="toast_simple_background_manual">请手动在设置中允许应用后台运行</string>

    <!-- 后台权限直接设置Toast -->
    <string name="toast_background_permission_direct_guide">请在应用信息中设置\'电池优化\'\'后台运行\'\'自启动\'为允许/不限制</string>
    <string name="toast_background_permission_direct_manual">请手动在设置中允许应用后台运行</string>

    <!-- 后台权限设置完成确认对话框 -->
    <string name="dialog_background_permission_completion_title">设置完成确认</string>
    <string name="dialog_background_permission_completion_message">您是否已经按照说明完成了启动管理设置？\n\n✓ 关闭"自动管理"\n✓ 开启"允许自启动"\n✓ 开启"允许关联启动"\n✓ 开启"允许后台活动"\n\n选择"是"后，系统将不再提醒此设置。</string>
    <string name="dialog_background_permission_completion_positive">是，已设置完成</string>
    <string name="dialog_background_permission_completion_negative">还没有，稍后再说</string>
    <string name="toast_background_permission_completion">启动管理权限设置完成！</string>

    <!-- 电源管理权限对话框 -->
    <string name="dialog_power_management_title">电源管理权限设置</string>
    <string name="dialog_power_management_message">为确保应用在退出时也能收到推送消息，需要设置以下权限：\n\n%1$s\n\n这些设置可以防止系统杀死推送服务，确保消息及时接收。</string>

    <!-- 电池优化对话框 -->
    <string name="dialog_battery_optimization_title">电池优化设置</string>
    <string name="dialog_battery_optimization_message">为确保应用能在后台正常接收消息，需要将应用加入电池优化白名单。\n\n点击"去设置"后：\n1. 在弹出的页面中找到"智能缝纫系统"\n2. 选择"不优化"或"允许"\n3. 返回应用继续设置</string>
    <string name="dialog_battery_optimization_positive">去设置</string>
    <string name="dialog_battery_optimization_negative">跳过</string>

    <!-- 电池优化手动指导 -->
    <string name="dialog_battery_manual_title">手动设置指导</string>
    <string name="dialog_battery_manual_message">请按以下步骤手动设置电池优化：\n\n方法一：\n1. 打开手机"设置"\n2. 找到"电池"或"电源管理"\n3. 找到"电池优化"或"应用耗电管理"\n4. 找到"智能缝纫系统"并设置为"不优化"\n\n方法二：\n1. 打开手机"设置"\n2. 找到"应用管理"或"应用信息"\n3. 找到"智能缝纫系统"\n4. 在应用详情中找到"电池"选项\n5. 关闭"电池优化"或设置为"无限制"</string>
    <string name="dialog_battery_manual_positive">我知道了</string>
    <string name="dialog_battery_manual_negative">重新尝试</string>

    <!-- 自启动权限对话框 -->
    <string name="dialog_autostart_title">自启动权限设置</string>
    <string name="dialog_autostart_message">为确保推送服务在应用退出后仍能运行，请开启应用的自启动权限。\n\n不同手机品牌的设置位置：\n• 小米：安全中心 > 应用管理 > 自启动管理\n• 华为：手机管家 > 应用启动管理\n• OPPO/一加：设置 > 应用管理 > 自启动\n• vivo：i管家 > 应用管理 > 自启动\n• 其他：设置 > 应用管理 > 自启动管理</string>
    <string name="dialog_autostart_positive">去设置</string>
    <string name="dialog_autostart_negative">跳过</string>

    <!-- 后台运行权限对话框 -->
    <string name="dialog_background_run_title">后台运行权限设置</string>
    <string name="dialog_background_run_message">为确保推送服务能在后台持续运行，请允许应用后台运行。\n\n设置方法：\n• 在应用信息页面中找到"电池"或"后台运行"选项\n• 设置为"不限制"或"允许后台运行"\n• 关闭"自适应电池"对本应用的限制</string>
    <string name="dialog_background_run_positive">去设置</string>
    <string name="dialog_background_run_negative">完成</string>

    <!-- 权限提醒对话框 -->
    <string name="dialog_permission_reminder_title">权限提醒</string>
    <string name="dialog_permission_reminder_message">检测到以下权限未设置，可能影响消息接收：\n\n%1$s\n\n是否现在设置？</string>
    <string name="dialog_permission_reminder_positive">立即设置</string>
    <string name="dialog_permission_reminder_negative">稍后提醒</string>

    <!-- 权限提醒对话框消息组件 -->
    <string name="dialog_permission_reminder_message_prefix">检测到以下权限未设置，可能影响消息接收：\n\n</string>
    <string name="dialog_permission_reminder_message_suffix">\n\n是否现在设置？</string>

    <!-- 简化后台权限对话框 -->
    <string name="dialog_simple_background_title">后台运行权限设置</string>
    <string name="dialog_simple_background_message">为确保消息推送，请在下个页面将\'电池优化\'\'后台运行\'\'自启动\'都设置为允许/不限制。</string>
    <string name="dialog_simple_background_positive">去设置</string>

    <!-- 启动管理权限设置对话框 -->
    <string name="dialog_startup_management_title">启动管理权限设置</string>
    <string name="dialog_startup_management_positive">去设置</string>
    <string name="dialog_startup_management_negative">稍后设置</string>

    <!-- 启动管理权限设置完成Toast -->
    <string name="toast_startup_management_complete">启动管理权限设置完成！</string>
    <string name="toast_startup_management_reset">启动管理权限设置状态已重置</string>

    <!-- 设置完成确认对话框 -->
    <string name="dialog_setup_confirmation_title">设置完成确认</string>
    <string name="dialog_setup_confirmation_message">您是否已经按照说明完成了启动管理设置？\n\n✓ 关闭自动管理\n✓ 允许自启动\n✓ 允许关联启动\n✓ 允许后台活动\n\n选择"是"后，系统将不再提醒此设置。</string>
    <string name="dialog_setup_confirmation_positive">是，已设置完成</string>
    <string name="dialog_setup_confirmation_negative">还没有，稍后再说</string>

    <!-- 后台权限设置完成确认对话框 -->
    <string name="dialog_background_setup_confirmation_title">设置完成确认</string>
    <string name="dialog_background_setup_confirmation_message">您是否已经按照说明完成了启动管理设置？\n\n✓ 关闭"自动管理"\n✓ 开启"允许自启动"\n✓ 开启"允许关联启动"\n✓ 开启"允许后台活动"\n\n选择"是"后，系统将不再提醒此设置。</string>
    <string name="dialog_background_setup_confirmation_positive">是，已设置完成</string>
    <string name="dialog_background_setup_confirmation_negative">还没有，稍后再说</string>

    <!-- Toast提示消息 -->
    <string name="toast_find_smart_sewing_system">请找到"智能缝纫系统"并设置为"不优化"</string>
    <string name="toast_find_battery_optimization">请在应用信息中找到电池优化设置</string>
    <string name="toast_allow_autostart">请将"智能缝纫系统"设置为允许自启动</string>
    <string name="toast_find_autostart_settings">请在应用信息中找到自启动相关设置</string>
    <string name="toast_manual_autostart">请手动在设置中开启应用自启动权限</string>
    <string name="toast_find_battery_background">请在应用信息中找到电池或后台运行设置</string>
    <string name="toast_manual_background_run">请手动在设置中允许应用后台运行</string>
    <string name="toast_set_power_management">请在电源管理中为应用设置合适的权限</string>
    <string name="toast_find_power_management">请在设置中找到电源管理相关选项</string>
    <string name="toast_manual_system_settings">请手动打开系统设置进行配置</string>
    <string name="toast_opening_battery_optimization">正在打开电池优化设置…</string>
    <string name="toast_device_not_support_direct">设备不支持直接设置电池优化，请手动设置</string>
    <string name="toast_battery_optimization_test_failed">电池优化权限测试失败: %1$s</string>
    <string name="toast_no_battery_optimization_needed">当前Android版本无需电池优化权限</string>
    <string name="toast_opening_battery_list">打开电池优化设置列表</string>
    <string name="toast_device_not_support_battery">设备不支持电池优化设置</string>
    <string name="toast_all_battery_methods_failed">所有电池优化设置方案都失败</string>
    <string name="toast_set_all_power_permissions_hint">请在应用信息中设置所有相关权限</string>
    <string name="toast_manual_background_run_hint">请手动在设置中允许应用后台运行</string>
    <string name="toast_set_all_power_permissions_hint_detailed">请在应用信息中设置\'电池优化\'\'后台运行\'\'自启动\'为允许/不限制</string>
    <string name="toast_background_dialog_reset">后台权限对话框设置状态已重置</string>

    <!-- 电源管理权限对话框按钮 -->
    <string name="dialog_power_management_positive">立即设置</string>
    <string name="dialog_power_management_negative">稍后设置</string>

    <!-- 电源管理权限状态 -->
    <string name="power_permission_battery_whitelist">电池优化白名单</string>
    <string name="power_permission_autostart">开机自启动</string>
    <string name="power_permission_background_run">后台运行</string>

    <!-- Voice to Text Activity -->
    <string name="voice_to_text_title">语音转文字</string>
    <string name="voice_to_text_test_whisper">测试Whisper</string>
    <string name="voice_to_text_start_record">开始录音</string>
    <string name="voice_to_text_stop_record">停止录音</string>
    <string name="voice_to_text_start_convert">开始转换</string>
    <string name="voice_to_text_force_cn">强制中文</string>
    <string name="voice_to_text_test_api">测试API</string>
    <string name="voice_to_text_json_convert">JSON转换</string>

    <!-- Voice Input Dialog -->
    <string name="dialog_voice_input_speak">请说话</string>
    <string name="dialog_voice_input_listening">正在听取...</string>
    <string name="dialog_voice_input_cancel">取消</string>
    <string name="dialog_voice_input_send">发送</string>
    <string name="dialog_voice_input_swipe_hint">上滑发送，下滑取消</string>

    <!-- Chat Items -->
    <string name="chat_text_sample">示例文本</string>
    <string name="voice_item_time">时间</string>
    <string name="voice_item_seconds">秒</string>
    <string name="voice_item_converting">转换中...</string>

    <!-- Test Battery Optimization -->
    <string name="test_battery_optimization">测试电池优化</string>

    <!-- Voice Recognition Toast Messages -->
    <string name="toast_no_recognition">未识别到语音</string>
    <string name="toast_voice_recognition_error">语音识别错误：%1$s</string>
    <string name="toast_save_voice_failed">保存语音失败</string>
    <string name="toast_voice_data_unavailable">语音数据不可用</string>
    <string name="toast_voice_file_not_found">语音文件未找到</string>
    <string name="toast_playback_failed">播放失败</string>
    <string name="toast_playback_failed_with_message">播放失败：%1$s</string>
    <string name="toast_invalid_voice_data">无效的语音数据</string>
    <string name="toast_voice_to_text_converting">正在转换语音为文字...</string>
    <string name="toast_no_text_content">没有文字内容</string>
    <string name="toast_voice_to_text_failed">语音转文字失败：%1$s</string>

    <!-- 推送服务通知消息 -->
    <string name="push_service_starting">推送服务正在启动…</string>
    <string name="push_service_running">推送服务运行中，连接正常</string>
    <string name="push_service_disconnected">推送服务已断开，等待重连…</string>
    <string name="push_service_connect_failed">推送服务连接失败，正在重试…</string>
    <string name="push_service_reconnecting">推送服务重连中…</string>

    <!-- 网络状态通知消息 -->
    <string name="network_normal_push_running">网络正常，推送服务运行中…</string>
    <string name="network_connected_mqtt_unreachable">网络已连接，但MQTT服务器不可达…</string>
    <string name="network_disconnected">网络已断开</string>
    <string name="network_connecting">网络连接中…</string>
    <string name="network_limited">网络连接受限</string>

    <!-- 推送服务日志消息 -->
    <string name="log_foreground_notification_updated">前台通知立即更新: %1$s</string>
    <string name="log_network_state_changed">网络状态变化: %1$s</string>
    <string name="log_mqtt_connectivity_result">MQTT连通性检测结果: %1$s</string>

</resources>