<resources>
    <string name="app_name">Supreme Smart Sewing System</string>
    <string name="checking_fingerprint_support">Memeriksa dukungan sidik jari…</string>
    
    <!-- APP Update Related -->
    <string name="update_title">Pembaruan Aplikasi</string>
    <string name="update_available">Versi baru terdeteksi</string>
    <string name="current_version">Versi saat ini: %s</string>
    <string name="new_version">Versi pembaruan: %s</string>
    <string name="update_content">Konten pembaruan</string>
    <string name="update_now">Perbarui sekarang</string>
    <string name="update_later">Perbarui nanti</string>
    <string name="downloading">Mengunduh…</string>
    <string name="download_progress">Progres unduhan: %d%%</string>
    <string name="download_completed">Unduhan selesai</string>
    <string name="download_failed">Unduhan gagal</string>
    <string name="install_now">Instal sekarang</string>
    <string name="checking_update">Memeriksa pembaruan…</string>
    <string name="no_update">Sudah versi terbaru</string>
    <string name="network_error">Koneksi jaringan gagal</string>
    <string name="file_size">Ukuran file: %s</string>
    <string name="download_speed">Kecepatan unduh: %s/s</string>
    <string name="cancel_download">Batalkan unduhan</string>
    <string name="retry_download">Coba unduh lagi</string>
    <string name="update_install_prompt">Unduhan selesai, klik untuk menginstal</string>
    <string name="update_install_failed">Instalasi gagal</string>
    <string name="update_permission_denied">Izin ditolak, tidak dapat menginstal</string>
    <string name="check_update">Periksa pembaruan</string>
    <string name="text_command">Perintah kontrol (tahan untuk berbicara)</string>
    <string name="text_chat">Obrolan suara</string>
    <string name="loading_update_content">Memuat konten pembaruan…</string>
    
    <!-- Main Activity -->
    <string name="hint_input_text">Silakan masukkan teks</string>
    <string name="voice_input">Input suara</string>
    <string name="scan_qrcode">Pindai kode QR</string>
    <string name="scan_barcode">Pindai kode</string>
    <string name="ocr">OCR</string>
    <string name="face_recognition">Pengenalan wajah</string>
    <string name="fingerprint_recognition">Pengenalan sidik jari</string>
    <string name="standard_serial_port">Port serial standar</string>
    <string name="usb_serial_port">Port serial USB</string>
    <string name="close">Tutup</string>
    
    <!-- Fingerprint Activity -->
    <string name="fingerprint_icon_desc">Ikon sidik jari</string>
    <string name="fingerprint_title">Pengenalan sidik jari</string>
    <string name="start_fingerprint_auth">Mulai autentikasi sidik jari</string>
    <string name="start_fingerprint_bind">Ikat sidik jari</string>
    <string name="start_fingerprint_unbind">Lepas ikatan sidik jari</string>
    <string name="start_fingerprint_login">Login dengan sidik jari</string>

    <!-- Face Detection Activity -->
    <string name="flashlight_desc">Lampu kilat</string>
    <string name="face_register">Daftar\nwajah</string>
    <string name="face_register_start">Mendaftarkan wajah</string>
    <string name="face_register_completed">Pendaftaran wajah selesai</string>
    <string name="face_hint">Silakan jaga wajah dalam bingkai, efek lebih baik dengan cahaya yang cukup</string>

    <!-- Serial Activity -->
    <string name="input_data_hint">Masukkan data yang akan dikirim (heksadesimal)</string>
    <string name="send">Kirim</string>
    <string name="start_polling">Mulai polling</string>
    <string name="stop_polling">Hentikan polling</string>
    <string name="clear">Hapus</string>
    <string name="received_data_label">Data yang diterima:</string>
    <string name="receive_data_error">Kesalahan menerima data: </string>
    <string name="input_valid_hex_data">Silakan masukkan data heksadesimal yang valid</string>
    
    <!-- Modbus Activity -->
    <string name="default_send_data">Klik tombol kirim untuk menjalankan pembacaan data Qixing</string>
    <string name="received_prefix">Diterima: </string>
    
    <!-- Dialog strings (from existing resources) -->
    <string name="app_dialog_title">Pemberitahuan</string>
    <string name="app_dialog_cancel">Batal</string>
    <string name="app_dialog_ok">OK</string>
    
    <!-- Activity Toast and Status Messages -->
    <string name="fingerprint_auth_title">Autentikasi sidik jari</string>
    <string name="fingerprint_auth_subtitle">Silakan letakkan jari pada sensor sidik jari</string>
    <string name="fingerprint_auth_description">Gunakan sidik jari Anda untuk autentikasi</string>
    <string name="fingerprint_auth_cancel">Batal</string>
    <string name="fingerprint_auth_success">Autentikasi sidik jari berhasil!</string>
    <string name="fingerprint_auth_failed">Autentikasi sidik jari gagal, silakan coba lagi</string>
    <string name="fingerprint_auth_error">Kesalahan autentikasi: %s</string>
    <string name="fingerprint_auth_prompt">Silakan lakukan autentikasi sidik jari…</string>
    <string name="fingerprint_support_available">Perangkat mendukung pengenalan sidik jari, dapat melakukan autentikasi</string>
    <string name="fingerprint_no_hardware">Perangkat tidak mendukung pengenalan sidik jari</string>
    <string name="fingerprint_hw_unavailable">Perangkat keras pengenalan sidik jari saat ini tidak tersedia</string>
    <string name="fingerprint_none_enrolled">Belum mendaftarkan sidik jari, silakan daftarkan sidik jari di pengaturan sistem terlebih dahulu</string>
    <string name="fingerprint_security_update_required">Perlu pembaruan keamanan</string>
    <string name="fingerprint_unknown_error">Kesalahan tidak dikenal</string>
    
    <!-- Serial Port Messages -->
    <string name="serial_init_success">Inisialisasi port serial berhasil</string>
    <string name="serial_init_failed">Inisialisasi port serial gagal</string>
    <string name="send_success">Pengiriman berhasil</string>
    <string name="send_failed">Pengiriman gagal</string>
    <string name="start_polling_msg">Mulai polling</string>
    <string name="stop_polling_msg">Hentikan polling</string>
    <string name="usb_device_not_found">Perangkat USB tidak ditemukan</string>
    <string name="usb_permission_granted">Izin USB diberikan</string>
    <string name="usb_permission_denied">Izin USB ditolak</string>
    <string name="usb_init_success">Inisialisasi port serial USB berhasil</string>
    <string name="usb_init_failed">Inisialisasi port serial USB gagal</string>
    <string name="serial_error">Kesalahan port serial: %1$s</string>

    <!-- Log Messages -->
    <string name="log_sync_send_response">Pengiriman sinkron menerima respons: %1$s</string>
    <string name="log_sync_send_failed">Pengiriman sinkron gagal: %1$s</string>
    <string name="log_voice_results">=========== onResults = %1$s</string>
    <string name="log_voice_matches_size">=========== matches.size() = %1$d</string>
    <string name="log_voice_error">Kesalahan pengenalan suara: %1$d</string>
    <string name="log_audio_error">Kesalahan audio</string>
    <string name="log_client_error">Kesalahan klien</string>
    <string name="log_insufficient_permissions">Izin tidak cukup</string>
    <string name="log_network_error">Kesalahan jaringan</string>
    <string name="log_no_match">Tidak ada hasil yang cocok</string>
    <string name="log_recognizer_busy">Pengenal sibuk</string>
    <string name="log_server_error">Kesalahan server</string>
    <string name="log_speech_timeout">Timeout tidak ada input suara</string>
    <string name="log_voice_ready">Siap memulai pengenalan suara</string>
    <string name="log_voice_begin">Mendeteksi awal suara</string>
    <string name="log_voice_end">Mendeteksi akhir suara</string>
    <string name="log_voice_start_failed">Gagal memulai pengenalan suara</string>
    <string name="log_fingerprint_auth_error">Kesalahan autentikasi: %1$s</string>
    <string name="log_fingerprint_auth_success">Autentikasi sidik jari berhasil</string>
    <string name="log_fingerprint_auth_failed">Autentikasi sidik jari gagal</string>
    <string name="log_camera_size_updated">Ukuran kamera diperbarui menjadi: %1$dx%2$d</string>
    <string name="log_upload_failed">Unggah gagal: %1$s</string>
    <string name="log_response_content">Konten respons: %1$s</string>

    <!-- Default Values -->
    <string name="default_send_data_serial" translatable="false">0203083600056794\n020300010001D5F9\n0203085200026789</string>

    <!-- Language Settings -->
    <string name="language_settings">Pengaturan bahasa</string>
    <string name="select_language">Pilih bahasa</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">English</string>
    <string name="language_vietnamese">Tiếng Việt</string>
    <string name="language_indonesian">Bahasa Indonesia</string>
    <string name="back">Kembali</string>
    <string name="confirm">Konfirmasi</string>

    <!-- Voice to Text Activity -->
    <string name="default_status_text">Memeriksa izin…</string>
    <string name="default_result_text">Hasil transkripsi akan ditampilkan di sini…</string>

    <!-- Voice Input Activity -->
    <string name="voice_input_title">Input suara (Versi dialog)</string>
    <string name="voice_recognition_result">Hasil pengenalan</string>
    <string name="voice_input_hint">Tahan tombol di bawah untuk memulai input suara…</string>
    <string name="voice_hold_to_speak">Tahan untuk berbicara</string>
    <string name="voice_convert_success">Konversi suara ke teks selesai</string>
    <string name="voice_no_speech_detected">Tidak ada suara yang dikenali</string>
    <string name="voice_input_cancelled">Input suara dibatalkan</string>
    <string name="voice_recognition_error">Kesalahan pengenalan suara: </string>

    <!-- Chat Activity -->
    <string name="voice_chat_title">Contoh obrolan suara</string>
    <string name="voice_input_message_hint">Masukkan pesan</string>
    <string name="voice_hold_to_speak_btn">Tahan untuk berbicara</string>
    <string name="voice_send_message_desc">Kirim pesan</string>

    <string name="toast_please_enable_manually_in_settings">Silakan aktifkan secara manual di pengaturan</string>
    <string name="toast_optional_permissions_denied">Izin opsional ditolak</string>

    <string name="dialog_power_management_settings_title">Pengaturan izin manajemen daya</string>
    <string name="dialog_power_management_settings_message_prefix">Untuk memastikan aplikasi dapat menerima pesan push bahkan saat keluar, perlu mengatur izin berikut:\n\n</string>
    <string name="dialog_power_management_settings_message_suffix">\n\nPengaturan ini mencegah sistem membunuh layanan push dan memastikan penerimaan pesan tepat waktu.</string>
    <string name="dialog_power_management_settings_positive_button">Atur sekarang</string>
    <string name="dialog_power_management_settings_negative_button">Atur nanti</string>

    <string name="dialog_battery_optimization_settings_title">Pengaturan optimasi baterai</string>
    <string name="dialog_battery_optimization_settings_message">Untuk memastikan aplikasi dapat menerima pesan normal di latar belakang, perlu menambahkan aplikasi ke daftar putih optimasi baterai.\n\nSetelah mengklik \"Buka pengaturan\":\n1. Temukan \"Sistem Jahit Cerdas\" di halaman popup\n2. Pilih \"Jangan optimalkan\" atau \"Izinkan\"\n3. Kembali ke aplikasi untuk melanjutkan pengaturan</string>
    <string name="dialog_battery_optimization_positive_button">Buka pengaturan</string>
    <string name="dialog_battery_optimization_negative_button">Lewati</string>

    <string name="dialog_manual_setup_guide_title">Panduan pengaturan manual</string>
    <string name="dialog_manual_setup_guide_message">Silakan ikuti langkah-langkah berikut untuk mengatur optimasi baterai secara manual:\n\nMetode 1:\n1. Buka \"Pengaturan\" ponsel\n2. Temukan \"Baterai\" atau \"Manajemen Daya\"\n3. Temukan \"Optimasi Baterai\" atau \"Manajemen Konsumsi Daya Aplikasi\"\n4. Temukan \"Sistem Jahit Cerdas\" dan atur ke \"Jangan optimalkan\"\n\nMetode 2:\n1. Buka \"Pengaturan\" ponsel\n2. Temukan \"Manajemen Aplikasi\" atau \"Info Aplikasi\"\n3. Temukan \"Sistem Jahit Cerdas\"\n4. Temukan opsi \"Baterai\" di detail aplikasi\n5. Matikan \"Optimasi Baterai\" atau atur ke \"Tidak terbatas\"</string>
    <string name="dialog_manual_setup_guide_positive_button">Saya mengerti</string>
    <string name="dialog_manual_setup_guide_negative_button">Coba lagi</string>

    <string name="dialog_autostart_settings_title">Pengaturan izin mulai otomatis</string>
    <string name="dialog_autostart_settings_message">Untuk memastikan layanan push masih dapat berjalan setelah aplikasi keluar, silakan aktifkan izin mulai otomatis aplikasi.\n\nLokasi pengaturan untuk berbagai merek ponsel:\n• Xiaomi: Pusat Keamanan > Manajemen Aplikasi > Manajemen Mulai Otomatis\n• Huawei: Manajer Ponsel > Manajemen Peluncuran Aplikasi\n• OPPO/OnePlus: Pengaturan > Manajemen Aplikasi > Mulai Otomatis\n• vivo: i Manajer > Manajemen Aplikasi > Mulai Otomatis\n• Lainnya: Pengaturan > Manajemen Aplikasi > Manajemen Mulai Otomatis</string>
    <string name="dialog_autostart_positive_button">Buka pengaturan</string>
    <string name="dialog_autostart_negative_button">Lewati</string>
    <string name="toast_set_autostart_hint">Silakan atur \"%1$s\" untuk mengizinkan mulai otomatis</string>
    <string name="toast_manual_autostart_hint">Silakan aktifkan izin mulai otomatis aplikasi secara manual di pengaturan</string>

    <string name="toast_set_battery_or_background_hint">Silakan temukan pengaturan baterai atau latar belakang di info aplikasi</string>
    <string name="toast_power_management_setup_completed">Pengaturan izin manajemen daya selesai!</string>
    <string name="toast_power_management_partially_granted">Beberapa izin masih perlu diatur secara manual</string>

    <string name="dialog_permission_reminder_positive_button">Atur sekarang</string>
    <string name="dialog_permission_reminder_negative_button">Ingatkan nanti</string>

    <string name="dialog_startup_management_settings_title">Pengaturan izin manajemen startup</string>
    <string name="dialog_startup_management_settings_positive_button">Buka pengaturan</string>
    <string name="dialog_startup_management_settings_negative_button">Atur nanti</string>

    <string name="dialog_startup_management_confirm_title">Konfirmasi penyelesaian pengaturan</string>
    <string name="dialog_startup_management_confirm_message">Apakah Anda telah menyelesaikan pengaturan manajemen startup sesuai panduan?\n\n✓ Matikan \"Manajemen Otomatis\"\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Startup Terkait\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"\n\nSetelah memilih \"Ya\", sistem tidak akan lagi mengingatkan pengaturan ini.</string>
    <string name="dialog_startup_management_confirm_positive_button">Ya, sudah diatur</string>
    <string name="dialog_startup_management_confirm_negative_button">Belum, ingatkan nanti</string>
    <string name="toast_startup_management_completed">Pengaturan izin manajemen startup selesai!</string>

    <string name="toast_background_permission_setup_completed">Pengaturan izin manajemen daya selesai, aplikasi dapat berjalan normal di latar belakang</string>
    <string name="toast_partial_power_permission_denied">Beberapa izin manajemen daya belum diatur: %1$s</string>
    <string name="toast_background_permission_dialog_reset">Status dialog izin latar belakang telah direset</string>

    <string name="power_status_storage_permission">Izin penyimpanan: </string>
    <string name="power_status_notification_permission">Izin notifikasi: </string>
    <string name="power_status_battery_optimization_whitelist">Daftar putih optimasi baterai: </string>
    <string name="power_status_joined">Bergabung</string>
    <string name="power_status_not_joined">Belum bergabung</string>
    <string name="power_status_autostart_permission">Izin mulai otomatis: </string>
    <string name="power_status_granted">Diberikan</string>
    <string name="power_status_not_granted">Belum diberikan</string>
    <string name="power_status_background_run_permission">Izin latar belakang: </string>
    <string name="power_status_allowed">Diizinkan</string>
    <string name="power_status_restricted">Dibatasi</string>
    <string name="power_status_entered">Masuk</string>
    <string name="power_status_normal">Normal</string>
    <string name="power_status_overall_status">Status keseluruhan: </string>
    <string name="power_status_permissions_to_set">Izin yang perlu diatur: </string>

    <!-- PermissionHelper Toast Messages -->
    <string name="toast_power_management_permission_completed">Pengaturan izin manajemen daya selesai, aplikasi dapat berjalan normal di latar belakang</string>
    <string name="toast_set_power_management_permissions">Silakan atur izin yang sesuai untuk aplikasi di manajemen daya</string>
    <string name="toast_find_power_management_settings">Silakan temukan opsi terkait manajemen daya di pengaturan</string>
    <string name="toast_device_not_support_battery_optimization">Perangkat tidak mendukung pengaturan optimasi baterai langsung, silakan atur secara manual</string>
    <string name="toast_android_no_battery_optimization">Versi Android saat ini tidak memerlukan izin optimasi baterai</string>
    <string name="toast_open_battery_optimization_list">Membuka daftar pengaturan optimasi baterai</string>
    <string name="toast_device_not_support_battery_settings">Perangkat tidak mendukung pengaturan optimasi baterai</string>
    <string name="toast_all_battery_optimization_failed">Semua metode pengaturan optimasi baterai gagal</string>
    <string name="toast_set_all_permissions_in_app_info">Silakan atur semua izin terkait di info aplikasi</string>
    <string name="toast_manual_allow_background_run">Silakan izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>
    <string name="toast_set_battery_background_autostart_permissions">Silakan atur \'Optimasi Baterai\' \'Latar Belakang\' \'Mulai Otomatis\' ke Diizinkan/Tidak terbatas di info aplikasi</string>

    <!-- PermissionHelper Dialog Messages -->
    <string name="dialog_background_run_settings_title">Pengaturan izin latar belakang</string>
    <string name="dialog_background_run_settings_message">Untuk memastikan push pesan, silakan atur \'Optimasi Baterai\', \'Latar Belakang\', \'Mulai Otomatis\' ke Diizinkan/Tidak terbatas di halaman berikutnya.</string>
    <string name="dialog_background_run_settings_positive_button">Buka pengaturan</string>
    <string name="dialog_background_run_settings_negative_button">Atur nanti</string>

    <string name="dialog_startup_management_settings_message_xiaomi">Untuk memastikan push pesan, silakan lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Matikan \"Manajemen Otomatis\"\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Startup Terkait\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"</string>
    <string name="dialog_startup_management_settings_message_huawei">Untuk memastikan push pesan, silakan lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Matikan \"Manajemen Otomatis\"\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Startup Terkait\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"</string>
    <string name="dialog_startup_management_settings_message_oppo">Untuk memastikan push pesan, silakan lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Startup Terkait\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"</string>
    <string name="dialog_startup_management_settings_message_vivo">Untuk memastikan push pesan, silakan lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"\n✓ Aktifkan \"Izinkan Konsumsi Daya Tinggi di Latar Belakang\"</string>
    <string name="dialog_startup_management_settings_message_samsung">Untuk memastikan push pesan, silakan lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Matikan \"Tidurkan aplikasi\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"</string>
    <string name="dialog_startup_management_settings_message_oneplus">Untuk memastikan push pesan, silakan lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Startup Terkait\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"</string>
    <string name="dialog_startup_management_settings_message_default">Untuk memastikan push pesan, silakan lakukan pengaturan berikut di halaman berikutnya:\n\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"\n✓ Matikan pembatasan optimasi baterai terkait</string>

    <!-- Startup management permission setting dialog -->
    <string name="dialog_startup_management_base_message">Untuk memastikan layanan push masih dapat berjalan setelah aplikasi keluar, silakan aktifkan izin mulai otomatis aplikasi.\n\nLokasi pengaturan untuk berbagai merek ponsel:\n</string>
    <string name="dialog_startup_management_huawei_honor_message">• Huawei/Honor: Manajer Ponsel > Manajemen Peluncuran Aplikasi\n</string>
    <string name="dialog_startup_management_xiaomi_redmi_message">• Xiaomi: Pusat Keamanan > Manajemen Aplikasi > Manajemen Mulai Otomatis\n</string>
    <string name="dialog_startup_management_oppo_message">• OPPO/OnePlus: Pengaturan > Manajemen Aplikasi > Mulai Otomatis\n</string>
    <string name="dialog_startup_management_vivo_message">• vivo: i Manajer > Manajemen Aplikasi > Mulai Otomatis\n</string>
    <string name="dialog_startup_management_oneplus_message">• OnePlus: Pengaturan > Manajemen Aplikasi > Mulai Otomatis\n</string>
    <string name="dialog_startup_management_generic_message">• Lainnya: Pengaturan > Manajemen Aplikasi > Manajemen Mulai Otomatis</string>

    <!-- Permission related Toast messages -->
    <string name="toast_permission_granted_success">Pemberian izin berhasil</string>
    <string name="toast_missing_essential_permissions">Izin penting yang hilang: %1$s\nSilakan aktifkan secara manual di pengaturan</string>
    <string name="toast_battery_optimization_success">Pengaturan optimasi baterai berhasil!</string>
    <string name="toast_manual_battery_optimization">Silakan atur daftar putih optimasi baterai secara manual</string>
    <string name="toast_power_management_complete_background">Pengaturan izin manajemen daya selesai, aplikasi dapat berjalan normal di latar belakang</string>
    <string name="toast_manual_remaining_permissions">Silakan selesaikan secara manual pengaturan izin yang tersisa untuk pengalaman terbaik</string>
    <string name="toast_background_permission_already_set">Izin latar belakang sudah diatur</string>

    <!-- Permission display names -->
    <string name="permission_name_record_audio">Mikrofon</string>
    <string name="permission_name_camera">Kamera</string>
    <string name="permission_name_storage">Penyimpanan</string>
    <string name="permission_name_notification">Notifikasi</string>
    <string name="permission_name_bluetooth">Bluetooth</string>
    <string name="permission_name_location">Lokasi</string>

    <!-- Power management permission names -->
    <string name="power_permission_battery_optimization">Daftar putih optimasi baterai</string>
    <string name="power_permission_auto_start">Mulai otomatis saat boot</string>

    <!-- Power management dialog messages -->
    <string name="dialog_power_management_intro">Untuk memastikan aplikasi dapat menerima pesan push bahkan saat keluar, perlu mengatur izin berikut:\n\n</string>
    <string name="dialog_power_management_outro">\n\nPengaturan ini mencegah sistem membunuh layanan push dan memastikan penerimaan pesan tepat waktu.</string>

    <!-- Battery optimization related Toast -->
    <string name="toast_battery_optimization_guide">Silakan temukan \"Sistem Jahit Cerdas\" dan atur ke \"Jangan optimalkan\"</string>
    <string name="toast_battery_optimization_app_info">Silakan temukan pengaturan optimasi baterai di info aplikasi</string>

    <!-- Battery optimization manual setup dialog -->
    <string name="dialog_battery_optimization_manual_title">Panduan pengaturan manual</string>
    <string name="dialog_battery_optimization_manual_message">Silakan ikuti langkah-langkah berikut untuk mengatur optimasi baterai secara manual:\n\nMetode 1:\n1. Buka \"Pengaturan\" ponsel\n2. Temukan \"Baterai\" atau \"Manajemen Daya\"\n3. Temukan \"Optimasi Baterai\" atau \"Manajemen Konsumsi Daya Aplikasi\"\n4. Temukan \"Sistem Jahit Cerdas\" dan atur ke \"Jangan optimalkan\"\n\nMetode 2:\n1. Buka \"Pengaturan\" ponsel\n2. Temukan \"Manajemen Aplikasi\" atau \"Info Aplikasi\"\n3. Temukan \"Sistem Jahit Cerdas\"\n4. Temukan opsi \"Baterai\" di detail aplikasi\n5. Matikan \"Optimasi Baterai\" atau atur ke \"Tidak terbatas\"</string>
    <string name="dialog_battery_optimization_manual_positive">Saya mengerti</string>
    <string name="dialog_battery_optimization_manual_negative">Coba lagi</string>

    <!-- Auto-start permission dialog -->
    <string name="dialog_auto_start_title">Pengaturan izin mulai otomatis</string>
    <string name="dialog_auto_start_message">Untuk memastikan layanan push masih dapat berjalan setelah aplikasi keluar, silakan aktifkan izin mulai otomatis aplikasi.\n\nLokasi pengaturan untuk berbagai merek ponsel:\n• Xiaomi: Pusat Keamanan > Manajemen Aplikasi > Manajemen Mulai Otomatis\n• Huawei: Manajer Ponsel > Manajemen Peluncuran Aplikasi\n• OPPO/OnePlus: Pengaturan > Manajemen Aplikasi > Mulai Otomatis\n• vivo: i Manajer > Manajemen Aplikasi > Mulai Otomatis\n• Lainnya: Pengaturan > Manajemen Aplikasi > Manajemen Mulai Otomatis</string>
    <string name="dialog_auto_start_positive">Buka pengaturan</string>
    <string name="dialog_auto_start_negative">Lewati</string>

    <!-- Auto-start related Toast -->
    <string name="toast_auto_start_guide">Silakan atur \"Sistem Jahit Cerdas\" untuk mengizinkan mulai otomatis</string>
    <string name="toast_auto_start_app_info">Silakan temukan pengaturan terkait mulai otomatis di info aplikasi</string>
    <string name="toast_auto_start_manual">Silakan aktifkan izin mulai otomatis aplikasi secara manual di pengaturan</string>

    <!-- Background run related Toast -->
    <string name="toast_background_run_app_info">Silakan temukan pengaturan baterai atau latar belakang di info aplikasi</string>
    <string name="toast_background_run_manual">Silakan izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>

    <!-- Power management completion related Toast -->
    <string name="toast_power_management_complete">Pengaturan izin manajemen daya selesai!</string>
    <string name="toast_partial_permissions_manual">Beberapa izin masih perlu diatur secara manual</string>

    <!-- Power status report -->
    <string name="power_status_header">Laporan status izin manajemen daya</string>
    <string name="power_status_android_version">Versi Android: </string>
    <string name="power_status_battery_optimization">Daftar putih optimasi baterai: </string>
    <string name="power_status_auto_start">Izin mulai otomatis saat boot: </string>
    <string name="power_status_background_run">Izin latar belakang: </string>
    <string name="power_status_doze_mode">Status mode Doze: </string>
    <string name="power_status_doze_active">Diaktifkan</string>
    <string name="power_status_doze_normal">Normal</string>
    <string name="power_status_doze_check_failed">Status mode Doze: Pemeriksaan gagal</string>
    <string name="power_status_overall">Status keseluruhan: </string>
    <string name="power_status_good">Baik</string>
    <string name="power_status_needs_optimization">Perlu optimasi</string>
    <string name="power_status_permissions_needed">Izin yang diperlukan: </string>

    <!-- Power setup completion Toast -->
    <string name="toast_power_setup_complete">Pengaturan izin manajemen daya selesai, aplikasi dapat berjalan normal di latar belakang</string>
    <string name="toast_power_setup_manual_remaining">Silakan selesaikan secara manual pengaturan izin yang tersisa untuk pengalaman terbaik</string>

    <!-- Power settings guidance Toast -->
    <string name="toast_power_settings_guide">Silakan atur izin yang sesuai untuk aplikasi di manajemen daya</string>
    <string name="toast_power_settings_find">Silakan temukan opsi terkait manajemen daya di pengaturan</string>
    <string name="toast_power_settings_manual">Silakan buka pengaturan sistem secara manual untuk konfigurasi</string>

    <!-- Battery optimization debug information -->
    <string name="debug_battery_optimization_start">Memulai pemeriksaan izin optimasi baterai…</string>
    <string name="debug_battery_optimization_status">Status optimasi baterai saat ini: </string>
    <string name="debug_battery_optimization_ignored">Diabaikan</string>
    <string name="debug_battery_optimization_not_ignored">Tidak diabaikan</string>
    <string name="debug_battery_optimization_intent_resolved">Intent pengaturan optimasi baterai berhasil diselesaikan</string>
    <string name="debug_battery_optimization_opening">Membuka pengaturan optimasi baterai…</string>
    <string name="debug_battery_optimization_intent_not_resolved">Intent pengaturan optimasi baterai tidak dapat diselesaikan</string>
    <string name="debug_battery_optimization_not_supported">Perangkat tidak mendukung pengaturan optimasi baterai langsung, silakan atur secara manual</string>
    <string name="debug_battery_optimization_failed">Tes izin optimasi baterai gagal</string>
    <string name="debug_battery_optimization_test_failed">Tes izin optimasi baterai gagal: </string>
    <string name="debug_battery_optimization_android_below_6">Versi Android di bawah 6.0 tidak memerlukan izin optimasi baterai</string>
    <string name="debug_battery_optimization_no_need">Versi Android saat ini tidak memerlukan izin optimasi baterai</string>
    <string name="debug_battery_optimization_fallback_start">Mencoba metode pengaturan optimasi baterai cadangan…</string>
    <string name="debug_battery_optimization_fallback_resolved">Intent pengaturan optimasi baterai cadangan berhasil diselesaikan</string>
    <string name="debug_battery_optimization_fallback_opening">Membuka daftar pengaturan optimasi baterai</string>
    <string name="debug_battery_optimization_fallback_not_resolved">Intent pengaturan optimasi baterai cadangan tidak dapat diselesaikan</string>
    <string name="debug_battery_optimization_fallback_not_supported">Perangkat tidak mendukung pengaturan optimasi baterai</string>
    <string name="debug_battery_optimization_fallback_failed">Pengaturan optimasi baterai cadangan gagal</string>
    <string name="debug_battery_optimization_all_failed">Semua metode pengaturan optimasi baterai gagal</string>

    <!-- Simplified background permission setting Toast -->
    <string name="toast_simple_background_app_info">Silakan atur semua izin terkait di info aplikasi</string>
    <string name="toast_simple_background_manual">Silakan izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>

    <!-- Background permission direct setting Toast -->
    <string name="toast_background_permission_direct_guide">Silakan atur \'Optimasi Baterai\' \'Latar Belakang\' \'Mulai Otomatis\' ke Diizinkan/Tidak terbatas di info aplikasi</string>
    <string name="toast_background_permission_direct_manual">Silakan izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>

    <!-- Background permission setup completion confirmation dialog -->
    <string name="dialog_background_permission_completion_title">Konfirmasi penyelesaian pengaturan</string>
    <string name="dialog_background_permission_completion_message">Apakah Anda telah menyelesaikan pengaturan manajemen startup sesuai panduan?\n\n✓ Matikan \"Manajemen Otomatis\"\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Startup Terkait\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"\n\nSetelah memilih \"Ya\", sistem tidak akan lagi mengingatkan pengaturan ini.</string>
    <string name="dialog_background_permission_completion_positive">Ya, sudah diatur</string>
    <string name="dialog_background_permission_completion_negative">Belum, ingatkan nanti</string>
    <string name="toast_background_permission_completion">Pengaturan izin manajemen startup selesai!</string>

    <!-- Power management permission dialog -->
    <string name="dialog_power_management_title">Pengaturan izin manajemen daya</string>
    <string name="dialog_power_management_message">Untuk memastikan aplikasi dapat menerima pesan push bahkan saat keluar, perlu mengatur izin berikut:\n\n%1$s\n\nPengaturan ini mencegah sistem membunuh layanan push dan memastikan penerimaan pesan tepat waktu.</string>

    <!-- Battery optimization dialog -->
    <string name="dialog_battery_optimization_title">Pengaturan optimasi baterai</string>
    <string name="dialog_battery_optimization_message">Untuk memastikan aplikasi dapat menerima pesan normal di latar belakang, perlu menambahkan aplikasi ke daftar putih optimasi baterai.\n\nSetelah mengklik \"Buka pengaturan\":\n1. Temukan \"Sistem Jahit Cerdas\" di halaman popup\n2. Pilih \"Jangan optimalkan\" atau \"Izinkan\"\n3. Kembali ke aplikasi untuk melanjutkan pengaturan</string>
    <string name="dialog_battery_optimization_positive">Buka pengaturan</string>
    <string name="dialog_battery_optimization_negative">Lewati</string>

    <!-- Battery optimization manual guide -->
    <string name="dialog_battery_manual_title">Panduan pengaturan manual</string>
    <string name="dialog_battery_manual_message">Silakan ikuti langkah-langkah berikut untuk mengatur optimasi baterai secara manual:\n\nMetode 1:\n1. Buka \"Pengaturan\" ponsel\n2. Temukan \"Baterai\" atau \"Manajemen Daya\"\n3. Temukan \"Optimasi Baterai\" atau \"Manajemen Konsumsi Daya Aplikasi\"\n4. Temukan \"Sistem Jahit Cerdas\" dan atur ke \"Jangan optimalkan\"\n\nMetode 2:\n1. Buka \"Pengaturan\" ponsel\n2. Temukan \"Manajemen Aplikasi\" atau \"Info Aplikasi\"\n3. Temukan \"Sistem Jahit Cerdas\"\n4. Temukan opsi \"Baterai\" di detail aplikasi\n5. Matikan \"Optimasi Baterai\" atau atur ke \"Tidak terbatas\"</string>
    <string name="dialog_battery_manual_positive">Saya mengerti</string>
    <string name="dialog_battery_manual_negative">Coba lagi</string>

    <!-- Auto-start permission dialog -->
    <string name="dialog_autostart_title">Pengaturan izin mulai otomatis</string>
    <string name="dialog_autostart_message">Untuk memastikan layanan push masih dapat berjalan setelah aplikasi keluar, silakan aktifkan izin mulai otomatis aplikasi.\n\nLokasi pengaturan untuk berbagai merek ponsel:\n• Xiaomi: Pusat Keamanan > Manajemen Aplikasi > Manajemen Mulai Otomatis\n• Huawei: Manajer Ponsel > Manajemen Peluncuran Aplikasi\n• OPPO/OnePlus: Pengaturan > Manajemen Aplikasi > Mulai Otomatis\n• vivo: i Manajer > Manajemen Aplikasi > Mulai Otomatis\n• Lainnya: Pengaturan > Manajemen Aplikasi > Manajemen Mulai Otomatis</string>
    <string name="dialog_autostart_positive">Buka pengaturan</string>
    <string name="dialog_autostart_negative">Lewati</string>

    <!-- Background run permission dialog -->
    <string name="dialog_background_run_title">Pengaturan izin latar belakang</string>
    <string name="dialog_background_run_message">Untuk memastikan layanan push dapat berjalan terus menerus di latar belakang, silakan izinkan aplikasi berjalan di latar belakang.\n\nMetode pengaturan:\n• Temukan opsi \"Baterai\" atau \"Latar Belakang\" di halaman info aplikasi\n• Atur ke \"Tidak terbatas\" atau \"Izinkan latar belakang\"\n• Matikan pembatasan \"Baterai Adaptif\" untuk aplikasi ini</string>
    <string name="dialog_background_run_positive">Buka pengaturan</string>
    <string name="dialog_background_run_negative">Selesai</string>

    <!-- Permission reminder dialog -->
    <string name="dialog_permission_reminder_title">Pengingat izin</string>
    <string name="dialog_permission_reminder_message">Izin berikut belum diatur dan mungkin mempengaruhi penerimaan pesan:\n\n%1$s\n\nAtur sekarang?</string>
    <string name="dialog_permission_reminder_positive">Atur sekarang</string>
    <string name="dialog_permission_reminder_negative">Ingatkan nanti</string>

    <!-- Permission reminder dialog message components -->
    <string name="dialog_permission_reminder_message_prefix">Izin berikut belum diatur dan mungkin mempengaruhi penerimaan pesan:\n\n</string>
    <string name="dialog_permission_reminder_message_suffix">\n\nAtur sekarang?</string>

    <!-- Simplified background permission dialog -->
    <string name="dialog_simple_background_title">Pengaturan izin latar belakang</string>
    <string name="dialog_simple_background_message">Untuk memastikan push pesan, silakan atur \'Optimasi Baterai\' \'Latar Belakang\' \'Mulai Otomatis\' ke Diizinkan/Tidak terbatas di halaman berikutnya.</string>
    <string name="dialog_simple_background_positive">Buka pengaturan</string>

    <!-- Startup management permission settings dialog -->
    <string name="dialog_startup_management_title">Pengaturan izin manajemen startup</string>
    <string name="dialog_startup_management_positive">Buka pengaturan</string>
    <string name="dialog_startup_management_negative">Atur nanti</string>

    <!-- Startup management permission setup completion Toast -->
    <string name="toast_startup_management_complete">Pengaturan izin manajemen startup selesai!</string>
    <string name="toast_startup_management_reset">Status pengaturan izin manajemen startup telah direset</string>

    <!-- Setup completion confirmation dialog -->
    <string name="dialog_setup_confirmation_title">Konfirmasi penyelesaian pengaturan</string>
    <string name="dialog_setup_confirmation_message">Apakah Anda telah menyelesaikan pengaturan manajemen startup sesuai panduan?\n\n✓ Matikan manajemen otomatis\n✓ Izinkan mulai otomatis\n✓ Izinkan startup terkait\n✓ Izinkan aktivitas latar belakang\n\nSetelah memilih \"Ya\", sistem tidak akan lagi mengingatkan pengaturan ini.</string>
    <string name="dialog_setup_confirmation_positive">Ya, sudah diatur</string>
    <string name="dialog_setup_confirmation_negative">Belum, ingatkan nanti</string>

    <!-- Background permission setup completion confirmation dialog -->
    <string name="dialog_background_setup_confirmation_title">Konfirmasi penyelesaian pengaturan</string>
    <string name="dialog_background_setup_confirmation_message">Apakah Anda telah menyelesaikan pengaturan manajemen startup sesuai panduan?\n\n✓ Matikan \"Manajemen Otomatis\"\n✓ Aktifkan \"Izinkan Mulai Otomatis\"\n✓ Aktifkan \"Izinkan Startup Terkait\"\n✓ Aktifkan \"Izinkan Aktivitas Latar Belakang\"\n\nSetelah memilih \"Ya\", sistem tidak akan lagi mengingatkan pengaturan ini.</string>
    <string name="dialog_background_setup_confirmation_positive">Ya, sudah diatur</string>
    <string name="dialog_background_setup_confirmation_negative">Belum, ingatkan nanti</string>

    <!-- Toast hint messages -->
    <string name="toast_find_smart_sewing_system">Silakan temukan \"Sistem Jahit Cerdas\" dan atur ke \"Jangan optimalkan\"</string>
    <string name="toast_find_battery_optimization">Silakan temukan pengaturan optimasi baterai di info aplikasi</string>
    <string name="toast_allow_autostart">Silakan atur \"Sistem Jahit Cerdas\" untuk mengizinkan mulai otomatis</string>
    <string name="toast_find_autostart_settings">Silakan temukan pengaturan terkait mulai otomatis di info aplikasi</string>
    <string name="toast_manual_autostart">Silakan aktifkan izin mulai otomatis aplikasi secara manual di pengaturan</string>
    <string name="toast_find_battery_background">Silakan temukan pengaturan baterai atau latar belakang di info aplikasi</string>
    <string name="toast_manual_background_run">Silakan izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>
    <string name="toast_set_power_management">Silakan atur izin yang sesuai untuk aplikasi di manajemen daya</string>
    <string name="toast_find_power_management">Silakan temukan opsi terkait manajemen daya di pengaturan</string>
    <string name="toast_manual_system_settings">Silakan buka pengaturan sistem secara manual untuk konfigurasi</string>
    <string name="toast_opening_battery_optimization">Membuka pengaturan optimasi baterai…</string>
    <string name="toast_device_not_support_direct">Perangkat tidak mendukung pengaturan optimasi baterai langsung, silakan atur secara manual</string>
    <string name="toast_battery_optimization_test_failed">Tes izin optimasi baterai gagal: %1$s</string>
    <string name="toast_no_battery_optimization_needed">Versi Android saat ini tidak memerlukan izin optimasi baterai</string>
    <string name="toast_opening_battery_list">Membuka daftar pengaturan optimasi baterai</string>
    <string name="toast_device_not_support_battery">Perangkat tidak mendukung pengaturan optimasi baterai</string>
    <string name="toast_all_battery_methods_failed">Semua metode pengaturan optimasi baterai gagal</string>
    <string name="toast_set_all_power_permissions_hint">Silakan atur semua izin terkait di info aplikasi</string>
    <string name="toast_manual_background_run_hint">Silakan izinkan aplikasi berjalan di latar belakang secara manual di pengaturan</string>
    <string name="toast_set_all_power_permissions_hint_detailed">Silakan atur \'Optimasi Baterai\', \'Latar Belakang\', dan \'Mulai Otomatis\' ke Diizinkan/Tidak terbatas di info aplikasi</string>
    <string name="toast_background_dialog_reset">Status dialog izin latar belakang telah direset</string>

    <!-- Power management permission dialog buttons -->
    <string name="dialog_power_management_positive">Atur sekarang</string>
    <string name="dialog_power_management_negative">Atur nanti</string>

    <!-- Power management permission status -->
    <string name="power_permission_battery_whitelist">Daftar putih optimasi baterai</string>
    <string name="power_permission_autostart">Mulai otomatis saat boot</string>
    <string name="power_permission_background_run">Latar belakang</string>

    <!-- Voice to Text Activity -->
    <string name="voice_to_text_title">Suara ke Teks</string>
    <string name="voice_to_text_test_whisper">Tes Whisper</string>
    <string name="voice_to_text_start_record">Mulai Rekam</string>
    <string name="voice_to_text_stop_record">Berhenti Rekam</string>
    <string name="voice_to_text_start_convert">Mulai Konversi</string>
    <string name="voice_to_text_force_cn">Paksa Bahasa Cina</string>
    <string name="voice_to_text_test_api">Tes API</string>
    <string name="voice_to_text_json_convert">Konversi JSON</string>

    <!-- Voice Input Dialog -->
    <string name="dialog_voice_input_speak">Silakan bicara</string>
    <string name="dialog_voice_input_listening">Mendengarkan...</string>
    <string name="dialog_voice_input_cancel">Batal</string>
    <string name="dialog_voice_input_send">Kirim</string>
    <string name="dialog_voice_input_swipe_hint">Geser ke atas untuk kirim, geser ke bawah untuk batal</string>

    <!-- Chat Items -->
    <string name="chat_text_sample">Contoh teks</string>
    <string name="voice_item_time">Waktu</string>
    <string name="voice_item_seconds">detik</string>
    <string name="voice_item_converting">Mengkonversi...</string>

    <!-- Test Battery Optimization -->
    <string name="test_battery_optimization">Tes Optimasi Baterai</string>

    <!-- Voice Recognition Toast Messages -->
    <string name="toast_no_recognition">Tidak ada suara yang dikenali</string>
    <string name="toast_voice_recognition_error">Kesalahan pengenalan suara: %1$s</string>
    <string name="toast_save_voice_failed">Gagal menyimpan suara</string>
    <string name="toast_voice_data_unavailable">Data suara tidak tersedia</string>
    <string name="toast_voice_file_not_found">File suara tidak ditemukan</string>
    <string name="toast_playback_failed">Pemutaran gagal</string>
    <string name="toast_playback_failed_with_message">Pemutaran gagal: %1$s</string>
    <string name="toast_invalid_voice_data">Data suara tidak valid</string>
    <string name="toast_voice_to_text_converting">Mengkonversi suara ke teks...</string>
    <string name="toast_no_text_content">Tidak ada konten teks</string>
    <string name="toast_voice_to_text_failed">Konversi suara ke teks gagal: %1$s</string>

    <!-- Push Service Notification Messages -->
    <string name="push_service_starting">Layanan push sedang memulai…</string>
    <string name="push_service_running">Layanan push berjalan, koneksi normal</string>
    <string name="push_service_disconnected">Layanan push terputus, menunggu koneksi ulang…</string>
    <string name="push_service_connect_failed">Koneksi layanan push gagal, mencoba lagi…</string>
    <string name="push_service_reconnecting">Layanan push sedang menyambung kembali…</string>

    <!-- Network Status Notification Messages -->
    <string name="network_normal_push_running">Jaringan normal, layanan push berjalan…</string>
    <string name="network_connected_mqtt_unreachable">Jaringan terhubung, tetapi server MQTT tidak dapat dijangkau…</string>
    <string name="network_disconnected">Jaringan terputus</string>
    <string name="network_connecting">Jaringan sedang terhubung…</string>
    <string name="network_limited">Koneksi jaringan terbatas</string>

    <!-- Push Service Log Messages -->
    <string name="log_foreground_notification_updated">Notifikasi latar depan diperbarui segera: %1$s</string>
    <string name="log_network_state_changed">Status jaringan berubah: %1$s</string>
    <string name="log_mqtt_connectivity_result">Hasil tes konektivitas MQTT: %1$s</string>

</resources>
