package com.supreme.smart.sewing.push;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.core.content.ContextCompat;

import androidx.core.app.NotificationCompat;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.supreme.smart.sewing.MainActivity;
import com.supreme.smart.sewing.R;

/**
 * 推送通知管理器
 * 负责创建和管理推送相关的通知
 */
public class PushNotificationManager {

    private static final String TAG = "NotificationManager";

    // 通知相关常量
    private static final String PUSH_CHANNEL_ID = "smart_sewing_push";
    private static final String SERVICE_CHANNEL_ID = "smart_sewing_service";
    private static final int FOREGROUND_NOTIFICATION_ID = 1001;
    private static final int PUSH_NOTIFICATION_ID = 1002;

    // 通知权限相关常量
    private static final String NOTIFICATION_PERMISSION = "android.permission.POST_NOTIFICATIONS";

    private Context context;
    private NotificationManager notificationManager;

    public PushNotificationManager(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context
                .getSystemService(Context.NOTIFICATION_SERVICE);
        createNotificationChannel();
    }

    /**
     * 创建通知渠道（已由 PermissionHelper 统一管理）
     */
    @SuppressLint("ObsoleteSdkInt")
    private void createNotificationChannel() {
        // 通知渠道已由 MainActivity 中的 PermissionHelper 统一创建
        // 这里只需要记录日志
        LogUtils.dTag(TAG, "通知渠道由 PermissionHelper 统一管理");
        LogUtils.dTag(TAG, "推送渠道ID: " + PUSH_CHANNEL_ID);
        LogUtils.dTag(TAG, "服务渠道ID: " + SERVICE_CHANNEL_ID);
    }

    /**
     * 检查通知权限（原生Android权限检查）
     */
    public boolean hasNotificationPermission() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 检查动态权限
                return ContextCompat.checkSelfPermission(context, NOTIFICATION_PERMISSION)
                    == PackageManager.PERMISSION_GRANTED;
            } else {
                // Android 12及以下检查通知是否被用户禁用
                return notificationManager.areNotificationsEnabled();
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "检查通知权限失败", e);
            return false;
        }
    }

    /**
     * 获取通知权限状态信息（用于调试）
     */
    @SuppressLint("ObsoleteSdkInt")
    public String getNotificationPermissionStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== 通知权限状态 ===\n");
        status.append("Android版本: ").append(Build.VERSION.SDK_INT).append("\n");

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            status.append("权限模式: 动态申请模式\n");
            boolean hasPermission = ContextCompat.checkSelfPermission(context, NOTIFICATION_PERMISSION)
                == PackageManager.PERMISSION_GRANTED;
            status.append("POST_NOTIFICATIONS: ").append(hasPermission).append("\n");
        } else {
            status.append("权限模式: 系统设置模式\n");
        }

        status.append("通知总开关: ").append(hasNotificationPermission()).append("\n");

        // 检查通知渠道状态
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                status.append("通知渠道数量: ").append(notificationManager.getNotificationChannels().size()).append("\n");
                status.append("推送渠道ID: ").append(PUSH_CHANNEL_ID).append("\n");
                status.append("服务渠道ID: ").append(SERVICE_CHANNEL_ID).append("\n");
            } catch (Exception e) {
                status.append("通知渠道检查失败: ").append(e.getMessage()).append("\n");
            }
        }

        return status.toString();
    }

    /**
     * 创建前台服务通知
     */
    public Notification createForegroundNotification(String content) {
        Intent notificationIntent = new Intent(context, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, notificationIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        return new NotificationCompat.Builder(context, SERVICE_CHANNEL_ID)
                .setContentTitle(context.getString(R.string.app_name))
                .setContentText(content)
                .setSmallIcon(getNotificationIcon())
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .setSilent(true)
                .build();
    }

    /**
     * 立即更新前台服务通知（无频率限制，用于重要状态变化）
     */
    public void updateForegroundNotification(String content) {
        Notification notification = createForegroundNotification(content);
        notificationManager.notify(FOREGROUND_NOTIFICATION_ID, notification);
        LogUtils.dTag(TAG, "前台通知立即更新: " + content);
    }

    /**
     * 显示推送消息通知
     */
    public void showPushNotification(String topic, String message) {
        try {
            LogUtils.dTag(TAG, "准备显示推送通知 - Topic: " + topic + ", Message: " + message);

            // 使用统一的通知权限检查
            if (!hasNotificationPermission()) {
                LogUtils.wTag(TAG, "通知权限被禁用，无法显示通知");
                LogUtils.dTag(TAG, getNotificationPermissionStatus()); // 输出详细状态用于调试
                ToastUtils.showShort("请在设置中开启通知权限以接收推送消息");
                return;
            }

            // 解析消息内容
//            JSONObject messageObj = JSON.parseObject(message);
//            String type = messageObj.getString("type");
            String type = "system";
            String title = "智能缝纫系统";
            String content = message;

            // 根据消息类型设置不同的标题和内容
//            switch (type) {
//                case "alert":
//                    title = "设备警报";
//                    content = messageObj.getString("message");
//                    break;
//                case "notification":
//                    title = messageObj.getString("title");
//                    content = messageObj.getString("message");
//                    break;
//                case "system":
//                    title = "系统通知";
//                    content = messageObj.getString("message");
//                    break;
//                default:
//                    content = messageObj.getString("message");
//                    break;
//            }

            // 创建点击意图
            Intent intent = new Intent(context, MainActivity.class);
            intent.putExtra("push_message", message);
            intent.putExtra("push_topic", topic);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

            PendingIntent pendingIntent = PendingIntent.getActivity(
                    context, 0, intent,
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            // 创建通知
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, PUSH_CHANNEL_ID)
                    .setContentTitle(title)
                    .setContentText(content)
                    .setSmallIcon(getNotificationIcon())
                    .setContentIntent(pendingIntent)
                    .setAutoCancel(true)
                    .setPriority(NotificationCompat.PRIORITY_DEFAULT);

            // 如果是警报类型，设置为高优先级
            if ("alert".equals(type)) {
                builder.setPriority(NotificationCompat.PRIORITY_HIGH)
                        .setDefaults(Notification.DEFAULT_ALL);
            }

            Notification notification = builder.build();
            notificationManager.notify(PUSH_NOTIFICATION_ID, notification);

            LogUtils.iTag(TAG, "推送通知已发送 - ID: " + PUSH_NOTIFICATION_ID + ", Title: " + title + ", Content: " + content);
            LogUtils.dTag(TAG, "通知渠道: " + PUSH_CHANNEL_ID + ", 图标: " + getNotificationIcon());
        } catch (Exception e) {
            LogUtils.eTag(TAG, "显示推送通知失败", e);

            // 如果解析失败，显示原始消息
            showSimpleNotification("推送消息", message, topic);
        }
    }

    /**
     * 显示简单通知
     *
     * @noinspection SameParameterValue
     */
    private void showSimpleNotification(String title, String content, String topic) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.putExtra("push_message", content);
        intent.putExtra("push_topic", topic);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

        PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, PUSH_CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(content)
                .setSmallIcon(getNotificationIcon())
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT);

        notificationManager.notify(PUSH_NOTIFICATION_ID, builder.build());
    }

    /**
     * 取消推送通知
     * @noinspection unused
     */
    public void cancelPushNotification() {
        notificationManager.cancel(PUSH_NOTIFICATION_ID);
    }

    /**
     * 取消所有通知
     * @noinspection unused
     */
    public void cancelAllNotifications() {
        notificationManager.cancelAll();
    }

    /**
     * 获取前台服务通知ID
     */
    public int getForegroundNotificationId() {
        return FOREGROUND_NOTIFICATION_ID;
    }

    /**
     * 获取通知图标，提供备用方案
     */
    private int getNotificationIcon() {
        try {
            // 尝试使用自定义图标
            int iconRes = R.drawable.ic_notification;
            LogUtils.dTag(TAG, "使用自定义通知图标: " + iconRes);
            return iconRes;
        } catch (Exception e) {
            // 如果自定义图标不存在，使用系统默认图标
            LogUtils.wTag(TAG, "自定义通知图标不存在，使用系统默认图标", e);
            return android.R.drawable.ic_dialog_info;
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        context = null;
        notificationManager = null;
    }
}
