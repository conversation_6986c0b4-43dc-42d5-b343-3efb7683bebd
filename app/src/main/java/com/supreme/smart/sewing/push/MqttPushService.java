package com.supreme.smart.sewing.push;

import android.app.Notification;
import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import com.blankj.utilcode.util.LogUtils;

import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.BackpressureOverflowStrategy;
import io.reactivex.rxjava3.core.BackpressureStrategy;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Scheduler;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.reactivex.rxjava3.subjects.PublishSubject;

/**
 * MQTT推送服务 - 使用RxJava实现，支持背压处理
 */
public class MqttPushService extends Service {

    private static final String TAG = "MqttPushService";

    // 组件管理器
    private MqttConnectionManager mqttConnectionManager;
    private NetworkStateMonitor networkStateMonitor;
    private PushNotificationManager notificationManager;

    // RxJava管理
    private CompositeDisposable compositeDisposable;
    private PublishSubject<MqttMessage> messageSubject;
    private PublishSubject<ConnectionEvent> connectionEventSubject;
    private Scheduler backgroundScheduler;

    // 电源优化配置
    private PowerOptimizationConfig powerConfig;

    // 背压配置常量
    private static final int MESSAGE_BUFFER_SIZE = 1000;
    private static final int CONNECTION_EVENT_BUFFER_SIZE = 100;
    private static final long MESSAGE_PROCESSING_DELAY_MS = 100;
    private static final long CONNECTION_RETRY_DELAY_MS = 5000;

    /**
     * MQTT消息数据类
     */
    private static class MqttMessage {
        final String topic;
        final String message;
        final long timestamp;

        MqttMessage(String topic, String message) {
            this.topic = topic;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }
    }

    /**
     * 连接事件数据类
     */
    private static class ConnectionEvent {
        enum Type { CONNECTED, DISCONNECTED, CONNECT_FAILED }

        final Type type;
        final Throwable error;
        final long timestamp;

        ConnectionEvent(Type type) {
            this(type, null);
        }

        ConnectionEvent(Type type, Throwable error) {
            this.type = type;
            this.error = error;
            this.timestamp = System.currentTimeMillis();
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtils.dTag(TAG, "MQTT推送服务创建 - 使用RxJava实现");

        // 初始化电源优化配置
        powerConfig = PowerOptimizationConfig.getInstance(this);
        LogUtils.iTag(TAG, "设备类型: " + powerConfig.getDeviceType());

        // 初始化RxJava组件
        initializeRxJavaComponents();

        // 初始化各个管理器
        initializeManagers();

        // 设置消息处理流
        setupMessageProcessingFlow();

        // 设置连接事件处理流
        setupConnectionEventFlow();

        // 初始化MQTT连接
        initMqttConnection();
    }

    /**
     * 初始化RxJava组件
     */
    private void initializeRxJavaComponents() {
        // 初始化CompositeDisposable用于管理所有订阅
        compositeDisposable = new CompositeDisposable();

        // 初始化Subject用于消息和事件流
        messageSubject = PublishSubject.create();
        connectionEventSubject = PublishSubject.create();

        // 根据电源配置选择调度器
        PowerOptimizationConfig.ServiceConfig serviceConfig = powerConfig.getServiceConfig();
        if (serviceConfig.useFixedThreadPool) {
            backgroundScheduler = Schedulers.from(Executors.newFixedThreadPool(serviceConfig.threadPoolSize));
            LogUtils.dTag(TAG, "使用固定线程池调度器，大小: " + serviceConfig.threadPoolSize);
        } else {
            backgroundScheduler = Schedulers.io();
            LogUtils.dTag(TAG, "使用IO调度器");
        }
    }

    /**
     * 初始化各个管理器
     */
    private void initializeManagers() {
        // 初始化通知管理器
        notificationManager = new PushNotificationManager(this);

        // 初始化MQTT连接管理器
        mqttConnectionManager = new MqttConnectionManager();
        mqttConnectionManager.setConnectionCallback(new RxMqttConnectionCallback());

        // 初始化网络状态监听器（必须在MQTT管理器之后）
        networkStateMonitor = new NetworkStateMonitor(this);
        networkStateMonitor.setManagers(notificationManager, mqttConnectionManager);
    }

    /**
     * 设置消息处理流 - 支持背压处理
     */
    private void setupMessageProcessingFlow() {
        Disposable messageDisposable = messageSubject
                .toFlowable(BackpressureStrategy.BUFFER) // 使用缓冲策略处理背压
                .onBackpressureBuffer(MESSAGE_BUFFER_SIZE, // 设置缓冲区大小
                        () -> LogUtils.wTag(TAG, "消息缓冲区溢出，开始丢弃旧消息"),
                        BackpressureOverflowStrategy.DROP_OLDEST) // 缓冲区满时丢弃最旧的消息
                .observeOn(backgroundScheduler) // 在后台线程处理
                .delay(MESSAGE_PROCESSING_DELAY_MS, TimeUnit.MILLISECONDS) // 防止处理过快
                .doOnNext(message -> LogUtils.dTag(TAG, "处理MQTT消息: " + message.topic))
                .subscribe(
                        this::processMessage, // 处理消息
                        throwable -> LogUtils.eTag(TAG, "消息处理流发生错误", throwable),
                        () -> LogUtils.dTag(TAG, "消息处理流完成")
                );

        compositeDisposable.add(messageDisposable);
    }

    /**
     * 设置连接事件处理流
     */
    private void setupConnectionEventFlow() {
        Disposable connectionDisposable = connectionEventSubject
                .toFlowable(BackpressureStrategy.LATEST) // 连接事件使用最新策略
                .onBackpressureBuffer(CONNECTION_EVENT_BUFFER_SIZE)
                .observeOn(AndroidSchedulers.mainThread()) // 在主线程更新UI
                .distinctUntilChanged(event -> event.type) // 去重相同类型的连续事件
                .doOnNext(event -> LogUtils.dTag(TAG, "处理连接事件: " + event.type))
                .subscribe(
                        this::processConnectionEvent,
                        throwable -> LogUtils.eTag(TAG, "连接事件处理流发生错误", throwable)
                );

        compositeDisposable.add(connectionDisposable);
    }

    /**
     * 处理MQTT消息
     */
    private void processMessage(MqttMessage message) {
        try {
            // 显示推送通知
            notificationManager.showPushNotification(message.topic, message.message);
            LogUtils.dTag(TAG, "消息处理完成: " + message.topic + " -> " + message.message);
        } catch (Exception e) {
            LogUtils.eTag(TAG, "处理推送消息失败", e);
        }
    }

    /**
     * 处理连接事件
     */
    private void processConnectionEvent(ConnectionEvent event) {
        switch (event.type) {
            case CONNECTED:
                LogUtils.dTag(TAG, "MQTT连接成功");
                notificationManager.updateForegroundNotification("推送服务运行中，连接正常");
                break;
            case DISCONNECTED:
                LogUtils.dTag(TAG, "MQTT连接断开");
                notificationManager.updateForegroundNotification("推送服务已断开，等待重连...");
                // 启动重连流
                startReconnectionFlow();
                break;
            case CONNECT_FAILED:
                LogUtils.eTag(TAG, "MQTT连接失败", event.error);
                notificationManager.updateForegroundNotification("推送服务连接失败，正在重试...");
                // 启动重连流
                startReconnectionFlow();
                break;
        }
    }

    /**
     * 启动重连流 - 使用指数退避策略
     */
    private void startReconnectionFlow() {
        Disposable reconnectDisposable = Observable.interval(CONNECTION_RETRY_DELAY_MS, TimeUnit.MILLISECONDS)
                .take(5) // 最多重试5次
                .observeOn(backgroundScheduler)
                .doOnNext(attempt -> LogUtils.dTag(TAG, "尝试重连MQTT，第" + (attempt + 1) + "次"))
                .flatMapCompletable(attempt ->
                    Completable.fromAction(() -> {
                        if (mqttConnectionManager != null) {
                            // 先断开再重新初始化连接
                            mqttConnectionManager.disconnect();
                            // 延迟后重新初始化
                            try {
                                Thread.sleep(1000); // 等待断开完成
                                mqttConnectionManager.initialize();
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                LogUtils.wTag(TAG, "重连过程被中断", e);
                            }
                        }
                    })
                    .delay((long) Math.pow(2, attempt) * 1000, TimeUnit.MILLISECONDS) // 指数退避
                )
                .subscribe(
                    () -> LogUtils.dTag(TAG, "重连流程完成"),
                    throwable -> LogUtils.eTag(TAG, "重连流程失败", throwable)
                );

        compositeDisposable.add(reconnectDisposable);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LogUtils.dTag(TAG, "MQTT推送服务启动 - RxJava版本 - flags: " + flags + ", startId: " + startId);

        // 启动前台服务
        startForegroundService();

        // 启动网络监听
        startNetworkMonitoring();

        // 启动电源状态监控
        startPowerMonitoring();

        // 启动服务监控流
        startServiceMonitoringFlow();

        // 返回START_STICKY确保服务被杀死后自动重启
        return START_STICKY;
    }

    /**
     * 启动服务监控流 - 监控服务健康状态
     */
    private void startServiceMonitoringFlow() {
        Disposable monitoringDisposable = Observable.interval(30, TimeUnit.SECONDS)
                .observeOn(backgroundScheduler)
                .doOnNext(tick -> LogUtils.dTag(TAG, "服务健康检查 - tick: " + tick))
                .subscribe(
                    tick -> {
                        // 检查各组件状态
                        boolean mqttHealthy = mqttConnectionManager != null && mqttConnectionManager.isConnected();
                        boolean networkHealthy = networkStateMonitor != null;

                        LogUtils.dTag(TAG, "服务状态 - MQTT: " + mqttHealthy + ", Network: " + networkHealthy);

                        if (!mqttHealthy) {
                            LogUtils.wTag(TAG, "检测到MQTT连接异常，尝试重连");
                            connectionEventSubject.onNext(new ConnectionEvent(ConnectionEvent.Type.DISCONNECTED));
                        }
                    },
                    throwable -> LogUtils.eTag(TAG, "服务监控流发生错误", throwable)
                );

        compositeDisposable.add(monitoringDisposable);
    }

    private void startNetworkMonitoring() {
        networkStateMonitor.startMonitoring();
    }

    /**
     * 启动电源状态监控
     */
    private void startPowerMonitoring() {
        if (powerConfig != null) {
            powerConfig.startMonitoring();
            LogUtils.iTag(TAG, "电源状态监控已启动");
        } else {
            LogUtils.wTag(TAG, "电源配置为空，无法启动电源监控");
        }
    }

    /**
     * 启动前台服务
     */
    private void startForegroundService() {
        Notification notification = notificationManager.createForegroundNotification("推送服务正在启动...");
        startForeground(notificationManager.getForegroundNotificationId(), notification);
    }

    /**
     * 初始化MQTT连接 - 使用RxJava延迟执行
     */
    private void initMqttConnection() {
        if (mqttConnectionManager != null) {
            // 使用电源优化配置的延迟时间
            long delayMs = powerConfig.getServiceConfig().handlerDelayMs;
            LogUtils.dTag(TAG, "延迟 " + delayMs + "ms 启动MQTT连接");

            Disposable initDisposable = Completable.fromAction(() -> mqttConnectionManager.initialize())
                    .delay(delayMs, TimeUnit.MILLISECONDS)
                    .subscribeOn(backgroundScheduler)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                        () -> LogUtils.dTag(TAG, "MQTT连接初始化完成"),
                        throwable -> LogUtils.eTag(TAG, "MQTT连接初始化失败", throwable)
                    );

            compositeDisposable.add(initDisposable);
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        LogUtils.dTag(TAG, "MQTT推送服务销毁 - 清理RxJava资源");

        // 停止所有RxJava订阅
        if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
            compositeDisposable.clear();
            LogUtils.dTag(TAG, "RxJava订阅已清理");
        }

        // 完成Subject流
        if (messageSubject != null && !messageSubject.hasComplete()) {
            messageSubject.onComplete();
        }
        if (connectionEventSubject != null && !connectionEventSubject.hasComplete()) {
            connectionEventSubject.onComplete();
        }

        // 停止电源状态监控
        if (powerConfig != null) {
            powerConfig.stopMonitoring();
            LogUtils.dTag(TAG, "电源状态监控已停止");
        }

        // 停止网络监听
        if (networkStateMonitor != null) {
            networkStateMonitor.release();
        }

        // 断开MQTT连接
        if (mqttConnectionManager != null) {
            mqttConnectionManager.release();
        }

        // 释放通知管理器
        if (notificationManager != null) {
            notificationManager.release();
        }

        super.onDestroy();
    }

    /**
     * MQTT连接状态回调 - RxJava版本
     */
    private class RxMqttConnectionCallback implements MqttConnectionManager.ConnectionCallback {

        @Override
        public void onConnected() {
            LogUtils.dTag(TAG, "MQTT连接成功回调");
            // 发送连接成功事件到流中
            connectionEventSubject.onNext(new ConnectionEvent(ConnectionEvent.Type.CONNECTED));
        }

        @Override
        public void onDisconnected() {
            LogUtils.dTag(TAG, "MQTT连接断开回调");
            // 发送连接断开事件到流中
            connectionEventSubject.onNext(new ConnectionEvent(ConnectionEvent.Type.DISCONNECTED));
        }

        @Override
        public void onConnectFailed(Throwable throwable) {
            LogUtils.eTag(TAG, "MQTT连接失败回调", throwable);
            // 发送连接失败事件到流中
            connectionEventSubject.onNext(new ConnectionEvent(ConnectionEvent.Type.CONNECT_FAILED, throwable));
        }

        @Override
        public void onMessageReceived(String topic, String message) {
            LogUtils.dTag(TAG, "收到MQTT消息: " + topic);
            // 发送消息到处理流中，由RxJava处理背压
            messageSubject.onNext(new MqttMessage(topic, message));
        }
    }



}
