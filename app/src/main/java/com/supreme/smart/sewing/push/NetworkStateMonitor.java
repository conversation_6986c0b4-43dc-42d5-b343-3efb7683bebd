package com.supreme.smart.sewing.push;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.util.Log;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;

import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.BackpressureStrategy;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.reactivex.rxjava3.subjects.BehaviorSubject;
import io.reactivex.rxjava3.subjects.PublishSubject;

/**
 * 网络状态监听器 - RxJava版本
 * 负责监听网络状态变化并通过响应式流处理
 */
public class NetworkStateMonitor {

    private static final String TAG = "NetworkStateMonitor";

    private ConnectivityManager connectivityManager;
    private ConnectivityManager.NetworkCallback networkCallback;

    // 通知管理器 - 直接管理网络状态通知
    private PushNotificationManager notificationManager;

    // MQTT连接管理器 - 用于检测MQTT服务器连通性
    private MqttConnectionManager mqttConnectionManager;

    // RxJava组件
    private CompositeDisposable compositeDisposable;
    private BehaviorSubject<NetworkState> networkStateSubject;
    private PublishSubject<NetworkEvent> networkEventSubject;

    // 背压和防抖配置
    private static final long DEBOUNCE_TIMEOUT_MS = 1000;
    private static final long MQTT_CHECK_TIMEOUT_MS = 3000;
    private static final int NETWORK_EVENT_BUFFER_SIZE = 50;

    /**
     * 网络状态枚举
     */
    public enum NetworkState {
        DISCONNECTED,           // 网络断开
        CONNECTED_UNVALIDATED,  // 网络连接但未验证
        CONNECTED_VALIDATED,    // 网络连接且已验证
        CONNECTED_LIMITED       // 网络连接但受限
    }

    /**
     * 网络事件数据类
     */
    public static class NetworkEvent {
        public final Network network;
        public final NetworkState state;
        public final NetworkCapabilities capabilities;
        public final long timestamp;

        public NetworkEvent(NetworkState state, Network network, NetworkCapabilities capabilities) {
            this.state = state;
            this.network = network;
            this.capabilities = capabilities;
            this.timestamp = System.currentTimeMillis();
        }
    }

    public NetworkStateMonitor(Context context) {
        connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        // 初始化RxJava组件
        initializeRxJavaComponents();

        // 设置网络状态处理流
        setupNetworkStateFlow();

        // 设置网络事件处理流
        setupNetworkEventFlow();

        // 初始化当前网络状态
        initializeCurrentNetworkState();
    }

    /**
     * 初始化RxJava组件
     */
    private void initializeRxJavaComponents() {
        compositeDisposable = new CompositeDisposable();

        // 使用BehaviorSubject保持最新的网络状态
        networkStateSubject = BehaviorSubject.createDefault(NetworkState.DISCONNECTED);

        // 使用PublishSubject处理网络事件
        networkEventSubject = PublishSubject.create();

        LogUtils.dTag(TAG, "RxJava组件初始化完成");
    }

    /**
     * 设置网络状态处理流
     */
    private void setupNetworkStateFlow() {
        Disposable stateDisposable = networkStateSubject
                .distinctUntilChanged() // 只处理状态变化
                .debounce(DEBOUNCE_TIMEOUT_MS, TimeUnit.MILLISECONDS) // 防抖处理
                .observeOn(AndroidSchedulers.mainThread())
                .doOnNext(state -> LogUtils.dTag(TAG, "网络状态变化: " + state))
                .subscribe(
                    this::handleNetworkStateChange,
                    throwable -> LogUtils.eTag(TAG, "网络状态流处理错误", throwable)
                );

        compositeDisposable.add(stateDisposable);
    }

    /**
     * 设置网络事件处理流 - 支持背压
     */
    private void setupNetworkEventFlow() {
        Disposable eventDisposable = networkEventSubject
                .toFlowable(BackpressureStrategy.BUFFER)
                .onBackpressureBuffer(NETWORK_EVENT_BUFFER_SIZE,
                        () -> LogUtils.wTag(TAG, "网络事件缓冲区溢出"),
                        io.reactivex.rxjava3.core.BackpressureOverflowStrategy.DROP_OLDEST)
                .observeOn(Schedulers.io()) // 在IO线程处理MQTT连通性检测
                .flatMap(event ->
                    // 异步检测MQTT连通性
                    Observable.fromCallable(() -> checkMqttConnectivity(event))
                            .timeout(MQTT_CHECK_TIMEOUT_MS, TimeUnit.MILLISECONDS)
                            .onErrorReturn(throwable -> {
                                LogUtils.wTag(TAG, "MQTT连通性检测超时", throwable);
                                return new MqttConnectivityResult(event, false, "检测超时");
                            })
                            .toFlowable(BackpressureStrategy.LATEST)
                )
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        this::handleMqttConnectivityResult,
                    throwable -> LogUtils.eTag(TAG, "网络事件流处理错误", throwable)
                );

        compositeDisposable.add(eventDisposable);
    }

    /**
     * MQTT连通性检测结果
     */
    private static class MqttConnectivityResult {
        final NetworkEvent networkEvent;
        final boolean isConnectable;
        final String message;

        MqttConnectivityResult(NetworkEvent networkEvent, boolean isConnectable, String message) {
            this.networkEvent = networkEvent;
            this.isConnectable = isConnectable;
            this.message = message;
        }
    }

    /**
     * 处理网络状态变化
     */
    private void handleNetworkStateChange(NetworkState state) {
        LogUtils.dTag(TAG, "处理网络状态变化: " + state);

        switch (state) {
            case DISCONNECTED:
                updateNotification("网络断开，等待网络恢复...", "🔴");
                break;
            case CONNECTED_UNVALIDATED:
                updateNotification("网络连接中，正在验证...", "🟡");
                break;
            case CONNECTED_LIMITED:
                updateNotification("网络受限，正在验证网络连接...", "🟡");
                break;
            case CONNECTED_VALIDATED:
                // 网络已验证，等待MQTT连通性检测结果
                updateNotification("网络已连接，检测服务状态...", "🟡");
                break;
        }
    }

    /**
     * 检测MQTT连通性
     */
    private MqttConnectivityResult checkMqttConnectivity(NetworkEvent event) {
        if (mqttConnectionManager == null) {
            return new MqttConnectivityResult(event, false, "MQTT管理器未初始化");
        }

        try {
            boolean canConnect = mqttConnectionManager.isMqttBrokerReachable();
            String message = canConnect ? "MQTT服务器可达" : "MQTT服务器不可达";
            return new MqttConnectivityResult(event, canConnect, message);
        } catch (Exception e) {
            LogUtils.eTag(TAG, "MQTT连通性检测异常", e);
            return new MqttConnectivityResult(event, false, "检测异常: " + e.getMessage());
        }
    }

    /**
     * 处理MQTT连通性检测结果
     */
    private void handleMqttConnectivityResult(MqttConnectivityResult result) {
        if (result.networkEvent.state == NetworkState.CONNECTED_VALIDATED) {
            if (result.isConnectable) {
                updateNotification("网络正常，推送服务运行中...", "🟢");
            } else {
                updateNotification("网络已连接，但MQTT服务器不可达...", "🟡");
            }
        }

        LogUtils.dTag(TAG, "MQTT连通性检测结果: " + result.message);
    }

    /**
     * 更新通知
     */
    private void updateNotification(String message, String emoji) {
        if (notificationManager != null) {
            notificationManager.updateForegroundNotification(message);
            LogUtils.wTag(TAG, emoji + " " + message);
        }
    }

    /**
     * 设置通知管理器和MQTT连接管理器
     */
    public void setManagers(PushNotificationManager notificationManager, MqttConnectionManager mqttConnectionManager) {
        this.notificationManager = notificationManager;
        this.mqttConnectionManager = mqttConnectionManager;
    }

    /**
     * 初始化当前网络状态
     */
    private void initializeCurrentNetworkState() {
        Disposable initDisposable = Completable.fromAction(() -> {
            try {
                Network activeNetwork = connectivityManager.getActiveNetwork();
                NetworkState initialState = NetworkState.DISCONNECTED;

                if (activeNetwork != null) {
                    NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
                    if (capabilities != null) {
                        initialState = determineNetworkState(capabilities);
                    }
                }

                LogUtils.dTag(TAG, "初始化网络状态: " + initialState);
                networkStateSubject.onNext(initialState);

            } catch (Exception e) {
                LogUtils.eTag(TAG, "初始化网络状态失败", e);
                networkStateSubject.onNext(NetworkState.DISCONNECTED);
            }
        })
        .subscribeOn(Schedulers.io())
        .subscribe(
            () -> LogUtils.dTag(TAG, "网络状态初始化完成"),
            throwable -> LogUtils.eTag(TAG, "网络状态初始化失败", throwable)
        );

        compositeDisposable.add(initDisposable);
    }

    /**
     * 根据网络能力确定网络状态
     */
    private NetworkState determineNetworkState(NetworkCapabilities capabilities) {
        boolean hasInternet = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        boolean isValidated = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);

        if (!hasInternet) {
            return NetworkState.DISCONNECTED;
        } else if (isValidated) {
            return NetworkState.CONNECTED_VALIDATED;
        } else {
            return NetworkState.CONNECTED_UNVALIDATED;
        }
    }

    /**
     * 开始监听网络状态 - RxJava版本
     */
    public void startMonitoring() {
        if (connectivityManager == null) {
            LogUtils.eTag(TAG, "ConnectivityManager未初始化");
            return;
        }

        networkCallback = new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(@NonNull Network network) {
                super.onAvailable(network);
                LogUtils.dTag(TAG, "网络连接可用: " + network);
                // 网络连接建立，等待onCapabilitiesChanged确认具体状态
            }

            @Override
            public void onLost(@NonNull Network network) {
                super.onLost(network);
                LogUtils.eTag(TAG, "网络连接丢失: " + network);

                // 发送网络断开事件
                NetworkEvent event = new NetworkEvent(NetworkState.DISCONNECTED, network, null);
                networkStateSubject.onNext(NetworkState.DISCONNECTED);
                networkEventSubject.onNext(event);
            }

            @Override
            public void onCapabilitiesChanged(@NonNull Network network,
                                              @NonNull NetworkCapabilities networkCapabilities) {
                super.onCapabilitiesChanged(network, networkCapabilities);

                // 确定网络状态
                NetworkState newState = determineNetworkState(networkCapabilities);

                // 发送状态变化
                networkStateSubject.onNext(newState);

                // 如果网络已验证，发送事件进行MQTT连通性检测
                if (newState == NetworkState.CONNECTED_VALIDATED) {
                    NetworkEvent event = new NetworkEvent(newState, network, networkCapabilities);
                    networkEventSubject.onNext(event);
                }

                LogUtils.dTag(TAG, "网络能力变化: " + newState + ", 网络: " + network);
            }
        };

        // 创建网络请求，监听所有网络变化
        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        NetworkRequest networkRequest = builder.build();

        try {
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback);
            LogUtils.dTag(TAG, "网络状态监听注册成功");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "注册网络状态监听失败", Log.getStackTraceString(e));
        }
    }

    /**
     * 停止监听网络状态 - RxJava版本
     */
    public void stopMonitoring() {
        if (networkCallback != null && connectivityManager != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
                networkCallback = null;
                LogUtils.dTag(TAG, "网络状态监听注销成功");
            } catch (IllegalArgumentException e) {
                LogUtils.wTag(TAG, "网络监听器未注册或已注销");
            }
        }
    }

    /**
     * 释放资源 - RxJava版本
     */
    public void release() {
        LogUtils.dTag(TAG, "开始释放NetworkStateMonitor资源");

        // 停止网络监听
        stopMonitoring();

        // 清理所有RxJava订阅
        if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
            compositeDisposable.clear();
            LogUtils.dTag(TAG, "RxJava订阅已清理");
        }

        // 完成Subject流
        if (networkStateSubject != null && !networkStateSubject.hasComplete()) {
            networkStateSubject.onComplete();
        }
        if (networkEventSubject != null && !networkEventSubject.hasComplete()) {
            networkEventSubject.onComplete();
        }

        // 清理引用
        connectivityManager = null;
        notificationManager = null;
        mqttConnectionManager = null;

        LogUtils.dTag(TAG, "NetworkStateMonitor资源释放完成");
    }

    /**
     * 获取当前网络状态Observable - 供外部订阅
     * @noinspection unused
     */
    public Observable<NetworkState> getNetworkStateObservable() {
        return networkStateSubject.hide().distinctUntilChanged();
    }

    /**
     * 获取网络事件Observable - 供外部订阅
     * @noinspection unused
     */
    public Observable<NetworkEvent> getNetworkEventObservable() {
        return networkEventSubject.hide();
    }
}
