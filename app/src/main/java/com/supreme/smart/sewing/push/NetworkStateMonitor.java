package com.supreme.smart.sewing.push;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;

import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.BackpressureStrategy;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.reactivex.rxjava3.subjects.BehaviorSubject;
import io.reactivex.rxjava3.subjects.PublishSubject;

/**
 * 网络状态监听器
 * 负责监听网络状态变化并通知回调
 */
public class NetworkStateMonitor {

    private static final String TAG = "NetworkStateMonitor";

    private ConnectivityManager connectivityManager;
    private ConnectivityManager.NetworkCallback networkCallback;

    // 状态缓存，避免重复通知
    private volatile boolean lastInternetState = false;

    // 通知管理器 - 直接管理网络状态通知
    private PushNotificationManager notificationManager;

    // MQTT连接管理器 - 用于检测MQTT服务器连通性
    private MqttConnectionManager mqttConnectionManager;

    // 电源优化相关
    private Handler debounceHandler;
    private HandlerThread backgroundThread;
    private Runnable pendingNetworkAvailableCallback;
    private Runnable pendingNetworkLostCallback;

    public NetworkStateMonitor(Context context) {
        connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        // 创建后台线程用于网络检测
        backgroundThread = new HandlerThread("NetworkStateMonitor");
        backgroundThread.start();
        debounceHandler = new Handler(backgroundThread.getLooper());

        // 初始化当前网络状态
        initializeCurrentNetworkState();
    }

    /**
     * 设置通知管理器和MQTT连接管理器
     */
    public void setManagers(PushNotificationManager notificationManager, MqttConnectionManager mqttConnectionManager) {
        this.notificationManager = notificationManager;
        this.mqttConnectionManager = mqttConnectionManager;
    }

    /**
     * 初始化当前网络状态
     */
    private void initializeCurrentNetworkState() {
        try {
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork != null) {
                NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
                if (capabilities != null) {
                    lastInternetState = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
                    LogUtils.dTag(TAG, "初始化网络状态: " + (lastInternetState ? "有互联网连接" : "无互联网连接"));
                }
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "初始化网络状态失败", e);
            lastInternetState = false;
        }
    }

    /**
     * 开始监听网络状态
     */
    public void startMonitoring() {
        if (connectivityManager == null) {
            LogUtils.eTag(TAG, "ConnectivityManager未初始化");
            return;
        }

        networkCallback = new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(@NonNull Network network) {
                super.onAvailable(network);
                LogUtils.dTag(TAG, "网络连接可用: " + network);
                // 网络连接建立，但不一定有互联网访问，等待onCapabilitiesChanged确认
            }

            @Override
            public void onLost(@NonNull Network network) {
                super.onLost(network);
                LogUtils.eTag(TAG, "网络连接丢失: " + network);

                // 网络完全断开，立即更新状态
                debounceHandler.post(() -> {
                    if (notificationManager != null) {
                        notificationManager.updateForegroundNotification("网络断开，等待网络恢复...");
                        LogUtils.eTag(TAG, "🔴 网络断开，等待网络恢复...");
                    }
                });
            }

            @Override
            public void onCapabilitiesChanged(@NonNull Network network,
                                              @NonNull NetworkCapabilities networkCapabilities) {
                super.onCapabilitiesChanged(network, networkCapabilities);

                // 检查网络是否有互联网访问能力且已验证
                boolean hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
                boolean isValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
                if (hasInternet && isValidated) {
                    // 网络已验证可访问互联网，检测MQTT服务器连通性
                    debounceHandler.post(() -> {
                        boolean canConnectToMqtt = false;
                        if (mqttConnectionManager != null) {
                            canConnectToMqtt = mqttConnectionManager.isMqttBrokerReachable();
                        }

                        if (notificationManager != null) {
                            if (canConnectToMqtt) {
                                notificationManager.updateForegroundNotification("网络正常，推送服务运行中...");
                                LogUtils.wTag(TAG, "🟢 网络正常，推送服务运行中...");
                            } else {
                                notificationManager.updateForegroundNotification("网络已连接，但MQTT服务器不可达...");
                                LogUtils.wTag(TAG, "🟡 网络已连接，但MQTT服务器不可达...");
                            }
                        }
                    });
                } else if (hasInternet) {
                    debounceHandler.post(() -> {
                        if (notificationManager != null) {
                            notificationManager.updateForegroundNotification("网络受限，正在验证网络连接...");
                            LogUtils.wTag(TAG, "🟡 网络受限，正在验证网络连接...");
                        }
                    });
                }
            }
        };

        // 创建网络请求，监听所有网络变化
        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        NetworkRequest networkRequest = builder.build();

        try {
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback);
            LogUtils.dTag(TAG, "网络状态监听注册成功");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "注册网络状态监听失败", e);
        }
    }

    /**
     * 停止监听网络状态
     */
    public void stopMonitoring() {
        if (networkCallback != null && connectivityManager != null) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
                networkCallback = null;
                LogUtils.dTag(TAG, "网络状态监听注销成功");
            } catch (IllegalArgumentException e) {
                LogUtils.wTag(TAG, "网络监听器未注册或已注销");
            }
        }

        // 释放后台线程资源
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            backgroundThread = null;
            debounceHandler = null;
            LogUtils.dTag(TAG, "网络监听后台线程已释放");
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        stopMonitoring();

        // 清理防抖回调
        if (debounceHandler != null) {
            if (pendingNetworkAvailableCallback != null) {
                debounceHandler.removeCallbacks(pendingNetworkAvailableCallback);
            }
            if (pendingNetworkLostCallback != null) {
                debounceHandler.removeCallbacks(pendingNetworkLostCallback);
            }
        }

        connectivityManager = null;
        debounceHandler = null;
        pendingNetworkAvailableCallback = null;
        pendingNetworkLostCallback = null;
    }
}
