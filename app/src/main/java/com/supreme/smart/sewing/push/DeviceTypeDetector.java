package com.supreme.smart.sewing.push;

import android.content.Context;

import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.LogUtils;

/**
 * 设备类型检测工具类（简化版）
 * 使用blankj工具库进行设备类型检测
 */
public class DeviceTypeDetector {
    
    private static final String TAG = "DeviceTypeDetector";
    
    // 设备类型枚举
    public enum DeviceType {
        PHONE,   // 手机
        TABLET   // 平板（包括工业平板）
    }
    
    // 缓存检测结果，避免重复计算
    private static DeviceType cachedDeviceType = null;
    
    /**
     * 检测设备类型（简化版本，优先使用blankj工具库）
     * 
     * @param context 上下文
     * @return 设备类型
     */
    public static DeviceType detectDeviceType(Context context) {
        if (cachedDeviceType != null) {
            return cachedDeviceType;
        }
        
        try {
            // 优先使用blankj工具库的平板检测方法
            boolean isTablet;
            try {
                isTablet = DeviceUtils.isTablet();
                LogUtils.dTag(TAG, "blankj工具库判断为平板: " + isTablet);
            } catch (Exception e) {
                LogUtils.wTag(TAG, "blankj工具库平板检测方法不可用: " + e.getMessage());
                // 如果方法不存在，使用备用的简单检测
                isTablet = isTabletBySimpleCheck(context);
                LogUtils.dTag(TAG, "备用方法判断为平板: " + isTablet);
            }
            
            // 检测是否为工业设备（可选的额外检测）
            boolean isIndustrialDevice = isIndustrialDevice();
            
            LogUtils.dTag(TAG, "设备检测结果:");
            LogUtils.dTag(TAG, "  平板检测: " + isTablet);
            LogUtils.dTag(TAG, "  工业设备检测: " + isIndustrialDevice);
            
            // 简化的判断逻辑：平板或工业设备都视为TABLET
            if (isTablet || isIndustrialDevice) {
                cachedDeviceType = DeviceType.TABLET;
                LogUtils.iTag(TAG, "检测为平板设备");
            } else {
                cachedDeviceType = DeviceType.PHONE;
                LogUtils.iTag(TAG, "检测为手机设备");
            }
            
            // 记录设备基本信息
            logDeviceBasicInfo();
            
        } catch (Exception e) {
            LogUtils.eTag(TAG, "设备类型检测失败，默认为手机", e);
            cachedDeviceType = DeviceType.PHONE;
        }
        
        return cachedDeviceType;
    }
    
    /**
     * 备用的简单平板检测方法
     */
    private static boolean isTabletBySimpleCheck(Context context) {
        try {
            // 使用Android系统的配置检测
            android.content.res.Configuration config = context.getResources().getConfiguration();
            int screenLayout = config.screenLayout & android.content.res.Configuration.SCREENLAYOUT_SIZE_MASK;
            
            return screenLayout == android.content.res.Configuration.SCREENLAYOUT_SIZE_LARGE ||
                   screenLayout == android.content.res.Configuration.SCREENLAYOUT_SIZE_XLARGE;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "备用平板检测失败", e);
            return false;
        }
    }
    
    /**
     * 检测是否为工业设备（简化版）
     */
    private static boolean isIndustrialDevice() {
        try {
            String manufacturer = DeviceUtils.getManufacturer().toLowerCase();
            String model = DeviceUtils.getModel().toLowerCase();
            
            // 常见工业设备关键词
            String[] industrialKeywords = {
                "advantech", "ibase", "axiomtek", "winmate", "aaeon",
                "industrial", "rugged", "panel", "hmi", "embedded"
            };
            
            // 检查制造商和型号
            for (String keyword : industrialKeywords) {
                if (manufacturer.contains(keyword) || model.contains(keyword)) {
                    LogUtils.dTag(TAG, "检测到工业设备关键词: " + keyword);
                    return true;
                }
            }
            
            return false;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "工业设备检测失败", e);
            return false;
        }
    }
    
    /**
     * 记录设备基本信息
     */
    private static void logDeviceBasicInfo() {
        try {
            LogUtils.iTag(TAG, "=== 设备基本信息 ===");
            LogUtils.iTag(TAG, "制造商: " + DeviceUtils.getManufacturer());
            LogUtils.iTag(TAG, "型号: " + DeviceUtils.getModel());
            LogUtils.iTag(TAG, "Android版本: " + DeviceUtils.getSDKVersionName());
            LogUtils.iTag(TAG, "设备类型: " + cachedDeviceType);
            LogUtils.iTag(TAG, "==================");
        } catch (Exception e) {
            LogUtils.eTag(TAG, "记录设备信息失败", e);
        }
    }

}
