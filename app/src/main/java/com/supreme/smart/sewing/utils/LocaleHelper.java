package com.supreme.smart.sewing.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;

import java.util.Locale;

/**
 * 语言切换工具类
 * Language switching utility class
 */
public class LocaleHelper {
    
    private static final String SELECTED_LANGUAGE = "Locale.Helper.Selected.Language";
    
    // 支持的语言代码
    public static final String LANGUAGE_CHINESE = "zh";
    public static final String LANGUAGE_ENGLISH = "en";
    public static final String LANGUAGE_VIETNAMESE = "vi";
    public static final String LANGUAGE_INDONESIAN = "in";
    
    /**
     * 设置应用语言
     * @param context 上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    @SuppressLint("ObsoleteSdkInt")
    public static Context setLocale(Context context, String language) {
        persist(context, language);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return updateResources(context, language);
        }
        
        return updateResourcesLegacy(context, language);
    }
    
    /**
     * 获取当前设置的语言
     * @param context 上下文
     * @return 语言代码
     */
    public static String getLanguage(Context context) {
        SharedPreferences preferences = getSharedPreferences(context);
        return preferences.getString(SELECTED_LANGUAGE, LANGUAGE_CHINESE);
    }

    /**
     * 保存语言设置
     * @param context 上下文
     * @param language 语言代码
     */
    private static void persist(Context context, String language) {
        SharedPreferences preferences = getSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(SELECTED_LANGUAGE, language);
        editor.apply();
    }

    /**
     * 获取SharedPreferences实例 - 避免使用已弃用的PreferenceManager
     * @param context 上下文
     * @return SharedPreferences实例
     */
    private static SharedPreferences getSharedPreferences(Context context) {
        // 直接使用Context.getSharedPreferences，避免使用已弃用的PreferenceManager
        return context.getSharedPreferences("locale_preferences", Context.MODE_PRIVATE);
    }
    
    /**
     * 更新资源配置 (Android N及以上)
     * @param context 上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    private static Context updateResources(Context context, String language) {
        Locale locale = getLocale(language);
        Configuration configuration = context.getResources().getConfiguration();
        configuration.setLocale(locale);
        configuration.setLayoutDirection(locale);
        
        return context.createConfigurationContext(configuration);
    }
    
    /**
     * 更新资源配置 (Android N以下) - 使用现代API
     * @param context 上下文
     * @param language 语言代码
     * @return 上下文
     * @noinspection deprecation
     */
    @SuppressLint("ObsoleteSdkInt")
    private static Context updateResourcesLegacy(Context context, String language) {
        Locale locale = getLocale(language);
        Locale.setDefault(locale);

        Configuration configuration = new Configuration(context.getResources().getConfiguration());

        // 使用现代API设置语言
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLocale(locale);
        } else {
            // API 17以下使用已弃用的方法
            configuration.locale = locale;
        }

        // 设置布局方向
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLayoutDirection(locale);
        }

        // 使用createConfigurationContext替代updateConfiguration
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            return context.createConfigurationContext(configuration);
        } else {
            // API 17以下使用已弃用的方法
            Resources resources = context.getResources();
            resources.updateConfiguration(configuration, resources.getDisplayMetrics());
            return context;
        }
    }
    
    /**
     * 根据语言代码获取Locale对象
     * @param language 语言代码
     * @return Locale对象
     */
    private static Locale getLocale(String language) {
        if (language == null || language.isEmpty()) {
            return Locale.getDefault();
        }

        switch (language) {
            case LANGUAGE_ENGLISH:
                return Locale.ENGLISH;
            case LANGUAGE_VIETNAMESE:
                return new Locale("vi", "VN"); // 越南语 - 越南
            case LANGUAGE_INDONESIAN:
                // 注意：印尼语的正确代码是"id"，不是"in"
                return new Locale("id", "ID"); // 印尼语 - 印度尼西亚
            case LANGUAGE_CHINESE:
                return Locale.SIMPLIFIED_CHINESE; // 简体中文
            default:
                // 尝试解析语言代码
                try {
                    if (language.contains("_") || language.contains("-")) {
                        String[] parts = language.split("[_-]");
                        if (parts.length >= 2) {
                            return new Locale(parts[0], parts[1]);
                        }
                    }
                    return new Locale(language);
                } catch (Exception e) {
                    // 如果解析失败，返回默认语言
                    return Locale.getDefault();
                }
        }
    }

}